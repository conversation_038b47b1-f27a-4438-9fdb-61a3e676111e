<?php

namespace App\Http\Requests\LeaveRequests;

use App\Enums\LeaveTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
            'leave_type' => ['required', 'string', Rule::in(LeaveTypeEnum::values())],
            'leave_date' => ['required', 'date', 'after_or_equal:today'],
            'reason' => ['required', 'string', 'max:500'],
            'attachment_path' => ['nullable', 'string', 'max:255'],
            'tasks' => ['sometimes', 'array'],
            'tasks.*.task_description' => ['required_with:tasks', 'string', 'max:1000'],
            'tasks.*.attachment_path' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'teacher_id.required' => 'Guru harus dipilih.',
            'teacher_id.exists' => 'Guru yang dipilih tidak valid.',
            'leave_type.required' => 'Jenis cuti harus dipilih.',
            'leave_type.in' => 'Jenis cuti yang dipilih tidak valid.',
            'leave_date.required' => 'Tanggal cuti harus diisi.',
            'leave_date.date' => 'Format tanggal cuti tidak valid.',
            'leave_date.after_or_equal' => 'Tanggal cuti tidak boleh kurang dari hari ini.',
            'reason.required' => 'Alasan cuti harus diisi.',
            'reason.max' => 'Alasan cuti maksimal 500 karakter.',
            'attachment_path.max' => 'Path file lampiran terlalu panjang.',
            'tasks.*.task_description.required_with' => 'Deskripsi tugas harus diisi.',
            'tasks.*.task_description.max' => 'Deskripsi tugas maksimal 1000 karakter.',
            'tasks.*.attachment_path.max' => 'Path file lampiran tugas terlalu panjang.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'guru',
            'leave_type' => 'jenis cuti',
            'leave_date' => 'tanggal cuti',
            'reason' => 'alasan cuti',
            'attachment_path' => 'file lampiran',
            'tasks' => 'daftar tugas',
            'tasks.*.task_description' => 'deskripsi tugas',
            'tasks.*.attachment_path' => 'file lampiran tugas',
        ];
    }
}

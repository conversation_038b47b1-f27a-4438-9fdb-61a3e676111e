<?php

namespace App\Enums;

enum ReligionEnum: string
{
    case ISLAM = 'islam';
    case KRISTEN = 'kristen';
    case KATOLIK = 'katolik';
    case HINDU = 'hindu';
    case BUDDHA = 'buddha';
    case KONGHUCU = 'konghu<PERSON>';

    /**
     * Get all religion options as an array
     */
    public static function options(): array
    {
        return [
            self::ISLAM->value => 'Islam',
            self::KRISTEN->value => 'Kristen',
            self::KATOLIK->value => 'Katolik',
            self::HINDU->value => 'Hindu',
            self::BUDDHA->value => 'Buddha',
            self::KONGHUCU->value => 'Kong<PERSON><PERSON>',
        ];
    }

    /**
     * Get all religion values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the display label for a religion value
     */
    public static function getLabel(?string $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get all religion labels
     *
     * @return array
     */
    public static function labels(): array
    {
        return array_values(self::options());
    }

    /**
     * Convert a label to the corresponding enum value
     *
     * @param string $label
     * @return string|null
     */
    public static function fromLabel(string $label): ?string
    {
        $options = array_flip(self::options());
        return $options[$label] ?? null;
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Add new columns
            $table->string('nis', 20)->unique()->after('user_id');
            $table->string('nisn', 20)->unique()->after('nis');
            $table->string('parent_name')->after('gender');
            $table->string('parent_phone', 15)->after('parent_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Drop added columns
            $table->dropColumn([
                'nis',
                'nisn',
                'parent_name',
                'parent_phone',
            ]);
        });
    }
};

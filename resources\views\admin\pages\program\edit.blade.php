@extends('admin.layouts.app')

@section('title', 'Edit Program')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Program',
        'breadcrumb' => 'Manajemen Program',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0">Form Edit Program</h5>
                        </div>
                        <div>
                            <a href="{{ route('admin.programs.index') }}" class="btn btn-ghost-info">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form id="editProgramForm" action="{{ route('admin.programs.update', $program->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Program Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Program</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Name -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Program <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name', $program->name) }}" placeholder="Masukkan nama program" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>

                                    <!-- Code -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="code" class="form-label">Kode Program <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="code" name="code"
                                                   value="{{ old('code', $program->code) }}" placeholder="Masukkan kode program" required>
                                            <div class="invalid-feedback" data-field="code"></div>
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <label for="description" class="form-label">Deskripsi</label>
                                            <textarea class="form-control" id="description" name="description"
                                                      rows="4" placeholder="Masukkan deskripsi program">{{ old('description', $program->description) }}</textarea>
                                            <div class="invalid-feedback" data-field="description"></div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                @foreach ($statuses as $value => $label)
                                                    <option value="{{ $value }}" {{ old('status', $program->status) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.programs.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Form submission
            $('#editProgramForm').on('submit', function(e) {
                e.preventDefault();

                // Reset previous validation errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // Show loading state
                const $submitBtn = $('#submit-btn');
                const originalText = $submitBtn.html();
                $submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...').prop('disabled', true);

                // Prepare form data
                const formData = new FormData(this);

                // Submit form via AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST', // Laravel handles PUT via _method field
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "{{ route('admin.programs.index') }}";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            $.each(errors, function(field, messages) {
                                const $input = $(`[name="${field}"]`);
                                $input.addClass('is-invalid');
                                $(`[data-field="${field}"]`).text(messages[0]);
                            });
                        } else {
                            let message = 'Terjadi kesalahan saat menyimpan data';
                            if (xhr.status === 403) {
                                message = 'Anda tidak memiliki izin untuk melakukan aksi ini';
                            } else if (xhr.status === 404) {
                                message = 'Data tidak ditemukan';
                            } else if (xhr.responseJSON?.message) {
                                message = xhr.responseJSON.message;
                            }
                            Swal.fire({
                                title: 'Error!',
                                text: message,
                                icon: 'error'
                            });
                        }
                    },
                    complete: function() {
                        $submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });

        async function handleEditProgram(buttonElement) {
            const form = document.getElementById('editProgramForm');
            const errorMsg = document.getElementById('program-error-msg');

            if (!form || !buttonElement) {
                console.error('Element not found!');
                return;
            }

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Save button content before modification
            const originalBtnContent = buttonElement.innerHTML;
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="ri-loader-4-line align-bottom animate-spin"></i> Memproses...';

            const formData = new FormData(form);

            try {
                const response = await fetch(form.action, {
                    method: 'POST', // Laravel handles PUT via _method field
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: result.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        window.location.href = '{{ route('admin.programs.index') }}';
                    });
                } else {
                    throw new Error(result.message || 'Terjadi kesalahan, silakan coba lagi.');
                }
            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: error.message,
                    icon: 'error'
                });
            } finally {
                // Restore button state
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalBtnContent;
            }
        }
    </script>
@endpush

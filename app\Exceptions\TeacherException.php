<?php

namespace App\Exceptions;

use Exception;

/**
 * Custom exception for teacher-related business logic errors
 */
class TeacherException extends Exception
{
    /**
     * Create a new teacher exception instance.
     */
    public function __construct(string $message = 'Teacher operation failed', int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Exception for when a teacher cannot be deleted due to existing assignments
     */
    public static function cannotDeleteWithAssignments(): self
    {
        return new self('Guru tidak dapat dihapus karena masih memiliki penugasan aktif');
    }

    /**
     * Exception for when a teacher cannot change their own status
     */
    public static function cannotChangeOwnStatus(): self
    {
        return new self('Tidak dapat mengubah status akun sendiri');
    }

    /**
     * Exception for when a teacher email is already in use
     */
    public static function emailAlreadyExists(string $email): self
    {
        return new self("Email '{$email}' sudah digunakan oleh guru lain");
    }

    /**
     * Exception for when a teacher username is already in use
     */
    public static function usernameAlreadyExists(string $username): self
    {
        return new self("Username '{$username}' sudah digunakan oleh guru lain");
    }

    /**
     * Exception for when a teacher has invalid qualifications for a subject
     */
    public static function invalidQualification(string $teacherName, string $subjectName): self
    {
        return new self("Guru '{$teacherName}' tidak memiliki kualifikasi untuk mengajar mata pelajaran '{$subjectName}'");
    }

    /**
     * Exception for when a teacher is already assigned to a subject in the same classroom
     */
    public static function duplicateAssignment(): self
    {
        return new self('Guru sudah ditugaskan untuk mata pelajaran ini di kelas yang sama');
    }

    /**
     * Exception for when trying to assign multiple homeroom teachers to the same classroom
     */
    public static function multipleHomeroomTeachers(): self
    {
        return new self('Kelas sudah memiliki wali kelas. Hanya satu guru yang dapat menjadi wali kelas per kelas');
    }

    /**
     * Exception for when a teacher assignment cannot be deleted due to ongoing classes
     */
    public static function cannotDeleteAssignmentWithOngoingClasses(): self
    {
        return new self('Penugasan guru tidak dapat dihapus karena masih ada jadwal kelas yang aktif');
    }
}
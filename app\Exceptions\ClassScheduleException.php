<?php

namespace App\Exceptions;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class ClassScheduleException extends Exception
{
    /**
     * @var int
     */
    protected $statusCode = Response::HTTP_BAD_REQUEST;

    /**
     * @var array
     */
    protected $errors = [];

    /**
     * @var array
     */
    protected $headers = [];

    /**
     * ClassScheduleException constructor.
     */
    public function __construct(
        string $message = 'Terjadi kesalahan pada jadwal kelas',
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        if ($statusCode !== null) {
            $this->statusCode = $statusCode;
        }

        $this->errors = $errors;
        $this->headers = $headers;
    }

    /**
     * Get the HTTP status code.
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the errors array.
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the headers array.
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Create a schedule conflict exception.
     */
    public static function scheduleConflict(string $message = 'Jadwal bentrok dengan jadwal yang sudah ada'): self
    {
        return new self($message, Response::HTTP_CONFLICT);
    }

    /**
     * Create a teacher conflict exception.
     */
    public static function teacherConflict(string $message = 'Guru sudah memiliki jadwal pada waktu yang sama'): self
    {
        return new self($message, Response::HTTP_CONFLICT);
    }

    /**
     * Create a validation exception.
     */
    public static function validationError(string $message = 'Data jadwal tidak valid'): self
    {
        return new self($message, Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Create a not found exception.
     */
    public static function notFound(string $message = 'Jadwal kelas tidak ditemukan'): self
    {
        return new self($message, Response::HTTP_NOT_FOUND);
    }
}
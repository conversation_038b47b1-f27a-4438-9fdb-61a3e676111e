<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SubjectRequests\SubjectFilterRequest;
use App\Http\Requests\SubjectRequests\SubjectStoreRequest;
use App\Http\Requests\SubjectRequests\SubjectUpdateRequest;
use App\Models\Program;
use App\Models\Subject;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use Throwable;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects
     */
    public function index(SubjectFilterRequest $request): View|JsonResponse
    {
        $query = Subject::with('program');

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatSubjectsForDatatable($query);
        }

        return view('admin.pages.subject.index', [
            'programs' => Program::where('status', 'active')->orderBy('name')->get(),
        ]);
    }

    /**
     * Apply filters to the query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by program
        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query;
    }

    /**
     * Format subjects for DataTable
     */
    protected function formatSubjectsForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', function ($row) {
                return $row->name;
            })
            ->editColumn('program', function ($row) {
                return $row->program ? $row->program->name : '-';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.subject._action', [
                    'id' => $row->id,
                    'edit' => route('admin.subjects.edit', $row->id),
                    'destroy' => route('admin.subjects.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new subject
     */
    public function create(): View
    {
        return view('admin.pages.subject.create', [
            'programs' => Program::where('status', 'active')->orderBy('name')->get(),
        ]);
    }

    /**
     * Store a newly created subject
     */
    public function store(SubjectStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $subject = Subject::create([
                'name' => $validated['name'],
                'program_id' => $validated['program_id'],
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil dibuat.',
                'data' => $subject->load('program'),
            ], Response::HTTP_CREATED);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat mata pelajaran.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified subject
     */
    public function edit(Subject $subject): View
    {
        return view('admin.pages.subject.edit', [
            'subject' => $subject,
            'programs' => Program::where('status', 'active')->orderBy('name')->get(),
        ]);
    }

    /**
     * Update the specified subject
     */
    public function update(SubjectUpdateRequest $request, Subject $subject): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $subject->update([
                'name' => $validated['name'],
                'program_id' => $validated['program_id'],
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil diperbarui.',
                'data' => $subject->load('program'),
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui mata pelajaran.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified subject
     */
    public function destroy(Subject $subject): JsonResponse
    {
        DB::beginTransaction();
        try {
            // Check if the subject has related data before deleting
            $hasTeacherAssignments = $subject->teacherAssignments()->exists();

            if ($hasTeacherAssignments) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mata pelajaran tidak dapat dihapus karena masih memiliki data terkait.',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $subject->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil dihapus.',
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus mata pelajaran.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

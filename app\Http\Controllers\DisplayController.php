<?php

namespace App\Http\Controllers;

use App\Enums\AttendanceStatusEnum;
use App\Enums\AttendantTypeEnum;
use App\Models\AcademicYear;
use App\Models\Attendance;
use App\Models\ClassSchedule;
use App\Models\LessonHour;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use stdClass;

class DisplayController extends Controller
{
    /**
     * Display the TV schedule page
     */
    public function tvDisplay(): View
    {
        // Get active academic year
        $academicYear = AcademicYear::where('status', 'active')->first();
        $useDemoData = false;

        if (! $academicYear) {
            // Create demo academic year if none exists
            $academicYear = $this->createDemoAcademicYear();
            $useDemoData = true;
        }

        // Get current day of week
        $today = Carbon::now();
        $dayOfWeek = strtolower($today->englishDayOfWeek);

        // Convert day name to match database format
        $dayMapping = [
            'monday' => 'monday',
            'tuesday' => 'tuesday',
            'wednesday' => 'wednesday',
            'thursday' => 'thursday',
            'friday' => 'friday',
            'saturday' => 'saturday',
            'sunday' => 'sunday',
        ];

        $dayOfWeek = $dayMapping[$dayOfWeek] ?? 'monday';

        // Get all lesson hours ordered by sequence
        $lessonHours = LessonHour::orderBy('sequence', 'asc')->get();

        // Use demo lesson hours if none exist or using demo data
        if ($lessonHours->isEmpty() || $useDemoData) {
            $lessonHours = $this->createDemoLessonHours();
        }

        // For demo purposes, always set the first lesson hour as active
        $currentTime = $today->format('H:i:s');
        $currentLessonHour = null;
        $nextLessonHour = null;
        $progressPercentage = 0;

        // Force jam ke-1 to be active for demo
        if ($useDemoData || true) { // Always use this for demo
            if ($lessonHours->isNotEmpty()) {
                // Get the first lesson hour (jam ke-1)
                $firstLessonHour = $lessonHours->first(function ($hour) {
                    return $hour->sequence == 1;
                });

                if ($firstLessonHour) {
                    $currentLessonHour = $firstLessonHour;
                    // Set progress to 50%
                    $progressPercentage = 50;

                    // Set the next lesson hour
                    $nextLessonHour = $lessonHours->first(function ($hour) {
                        return $hour->sequence == 2;
                    });
                }
            }
        } else {
            // Original logic for production
            foreach ($lessonHours as $index => $lessonHour) {
                $startTime = Carbon::parse($lessonHour->start_time);
                $endTime = Carbon::parse($lessonHour->end_time);

                if ($today->between($startTime, $endTime)) {
                    $currentLessonHour = $lessonHour;

                    // Calculate progress percentage
                    $totalDuration = $startTime->diffInSeconds($endTime);
                    $elapsedDuration = $startTime->diffInSeconds($today);
                    $progressPercentage = min(100, round(($elapsedDuration / $totalDuration) * 100));

                    // Get next lesson hour if available
                    if (isset($lessonHours[$index + 1])) {
                        $nextLessonHour = $lessonHours[$index + 1];
                    }

                    break;
                } elseif ($today->lt($startTime)) {
                    // If we haven't reached this lesson hour yet, it's the next one
                    $nextLessonHour = $lessonHour;
                    break;
                }
            }
        }

        // If no current or next lesson hour is found, set a demo one for display purposes
        if (! $currentLessonHour && ! $nextLessonHour && ! $useDemoData) {
            // Find the first lesson hour of the day and set it as current with 50% progress
            if ($lessonHours->isNotEmpty()) {
                $currentLessonHour = $lessonHours->first();
                $progressPercentage = 50;

                if ($lessonHours->count() > 1) {
                    $nextLessonHour = $lessonHours[1];
                }
            }
        }

        // Get active schedules for today
        if ($useDemoData) {
            // Use demo schedules and attendances
            $activeSchedules = $this->createDemoSchedules($lessonHours, $dayOfWeek);
            $todayAttendances = $this->createDemoAttendances($activeSchedules);
        } else {
            $activeSchedules = ClassSchedule::with([
                'teacherAssignment.teacher.user',
                'teacherAssignment.subject',
                'teacherAssignment.classroom',
                'lessonHour',
            ])
                ->whereHas('teacherAssignment', function ($query) use ($academicYear) {
                    $query->where('academic_year_id', $academicYear->id);
                })
                ->where('day_of_week', $dayOfWeek)
                ->where('status', 'active')
                ->orderBy('lesson_hour_id', 'asc')
                ->get();

            // If no schedules found, use demo data
            if ($activeSchedules->isEmpty()) {
                $activeSchedules = $this->createDemoSchedules($lessonHours, $dayOfWeek);
                $todayAttendances = $this->createDemoAttendances($activeSchedules);
            } else {
                // Get attendance for today's schedules
                $todayAttendances = Attendance::whereIn('class_schedule_id', $activeSchedules->pluck('id'))
                    ->whereDate('attendance_date', $today->toDateString())
                    ->get()
                    ->groupBy('class_schedule_id');
            }
        }

        // Prepare schedules with attendance info
        $currentSchedules = collect();
        $upcomingSchedules = collect();

        foreach ($activeSchedules as $schedule) {
            // Add attendance info to schedule
            $schedule->attendances = $todayAttendances->get($schedule->id, collect());

            // Check if this schedule is current or upcoming
            if ($currentLessonHour && $schedule->lesson_hour_id == $currentLessonHour->id) {
                $currentSchedules->push($schedule);
            } elseif ($nextLessonHour && $schedule->lesson_hour_id == $nextLessonHour->id) {
                $upcomingSchedules->push($schedule);
            } elseif (! $currentLessonHour && ! $nextLessonHour && $schedule->lesson_hour_id > ($lessonHours->last()->id ?? 0)) {
                // If no current or next lesson hour, show the last schedules of the day
                $upcomingSchedules->push($schedule);
            }
        }

        // If no current schedules, assign some from active schedules for demo purposes
        if ($currentSchedules->isEmpty() && $activeSchedules->isNotEmpty() && $currentLessonHour) {
            // Find schedules that could be current based on lesson hour sequence
            $possibleCurrentSchedules = $activeSchedules->filter(function ($schedule) use ($currentLessonHour) {
                return $schedule->lessonHour->sequence == $currentLessonHour->sequence;
            });

            if ($possibleCurrentSchedules->isEmpty()) {
                // Just take the first few schedules as current
                $currentSchedules = $activeSchedules->take(min(3, $activeSchedules->count()));
            } else {
                $currentSchedules = $possibleCurrentSchedules;
            }
        }

        // If no upcoming schedules, assign some from active schedules for demo purposes
        if ($upcomingSchedules->isEmpty() && $activeSchedules->isNotEmpty() && $nextLessonHour) {
            // Find schedules that could be upcoming based on lesson hour sequence
            $possibleUpcomingSchedules = $activeSchedules->filter(function ($schedule) use ($nextLessonHour) {
                return $schedule->lessonHour->sequence == $nextLessonHour->sequence;
            });

            if ($possibleUpcomingSchedules->isEmpty()) {
                // Just take some schedules as upcoming, different from current ones
                $remainingSchedules = $activeSchedules->diff($currentSchedules);
                $upcomingSchedules = $remainingSchedules->take(min(3, $remainingSchedules->count()));
            } else {
                $upcomingSchedules = $possibleUpcomingSchedules;
            }
        }

        // Days mapping for display
        $days = [
            'monday' => 'Senin',
            'tuesday' => 'Selasa',
            'wednesday' => 'Rabu',
            'thursday' => 'Kamis',
            'friday' => 'Jumat',
            'saturday' => 'Sabtu',
            'sunday' => 'Minggu',
        ];

        // Uncomment this line to use the new view
        return view('display.tv-new', compact(
            'academicYear',
            'today',
            'dayOfWeek',
            'days',
            'currentLessonHour',
            'nextLessonHour',
            'progressPercentage',
            'currentSchedules',
            'upcomingSchedules',
            'activeSchedules'
        ));
    }

    /**
     * Create a demo academic year for display purposes
     */
    private function createDemoAcademicYear(): stdClass
    {
        $academicYear = new stdClass;
        $academicYear->id = 1;
        $academicYear->name = '2023/2024';
        $academicYear->semester = 'GANJIL';
        $academicYear->start_date = '2023-07-01';
        $academicYear->end_date = '2023-12-31';
        $academicYear->status = 'active';

        return $academicYear;
    }

    /**
     * Create demo lesson hours for display purposes
     */
    private function createDemoLessonHours(): Collection
    {
        $lessonHours = collect();

        // Create demo lesson hours based on typical school schedule
        $hours = [
            ['id' => 1, 'name' => 'Jam ke-1', 'start_time' => '07:30:00', 'end_time' => '08:15:00', 'sequence' => 1],
            ['id' => 2, 'name' => 'Jam ke-2', 'start_time' => '08:15:00', 'end_time' => '09:00:00', 'sequence' => 2],
            ['id' => 3, 'name' => 'Jam ke-3', 'start_time' => '09:00:00', 'end_time' => '09:45:00', 'sequence' => 3],
            ['id' => 4, 'name' => 'Istirahat', 'start_time' => '09:45:00', 'end_time' => '10:15:00', 'sequence' => 4],
            ['id' => 5, 'name' => 'Jam ke-4', 'start_time' => '10:15:00', 'end_time' => '11:00:00', 'sequence' => 5],
            ['id' => 6, 'name' => 'Jam ke-5', 'start_time' => '11:00:00', 'end_time' => '11:45:00', 'sequence' => 6],
            ['id' => 7, 'name' => 'Jam ke-6', 'start_time' => '11:45:00', 'end_time' => '12:30:00', 'sequence' => 7],
            ['id' => 8, 'name' => 'Istirahat & Sholat', 'start_time' => '12:30:00', 'end_time' => '13:30:00', 'sequence' => 8],
            ['id' => 9, 'name' => 'Jam ke-7', 'start_time' => '13:30:00', 'end_time' => '14:15:00', 'sequence' => 9],
            ['id' => 10, 'name' => 'Jam ke-8', 'start_time' => '14:15:00', 'end_time' => '15:00:00', 'sequence' => 10],
        ];

        foreach ($hours as $hour) {
            $lessonHour = new stdClass;
            $lessonHour->id = $hour['id'];
            $lessonHour->name = $hour['name'];
            $lessonHour->start_time = $hour['start_time'];
            $lessonHour->end_time = $hour['end_time'];
            $lessonHour->sequence = $hour['sequence'];
            $lessonHour->formatted_start_time = substr($hour['start_time'], 0, 5);
            $lessonHour->formatted_end_time = substr($hour['end_time'], 0, 5);

            $lessonHours->push($lessonHour);
        }

        return $lessonHours;
    }

    /**
     * Create demo schedules for display purposes
     */
    private function createDemoSchedules(Collection $lessonHours, string $dayOfWeek): Collection
    {
        $schedules = collect();

        // Create demo subjects
        $subjects = [
            ['id' => 1, 'name' => 'Matematika'],
            ['id' => 2, 'name' => 'Bahasa Indonesia'],
            ['id' => 3, 'name' => 'Bahasa Inggris'],
            ['id' => 4, 'name' => 'Fisika'],
            ['id' => 5, 'name' => 'Kimia'],
            ['id' => 6, 'name' => 'Biologi'],
            ['id' => 7, 'name' => 'Sejarah'],
            ['id' => 8, 'name' => 'Geografi'],
            ['id' => 9, 'name' => 'Ekonomi'],
            ['id' => 10, 'name' => 'Pendidikan Agama'],
        ];

        // Create demo classrooms
        $classrooms = [
            ['id' => 1, 'name' => 'X IPA 1'],
            ['id' => 2, 'name' => 'X IPA 2'],
            ['id' => 3, 'name' => 'X IPS 1'],
            ['id' => 4, 'name' => 'XI IPA 1'],
            ['id' => 5, 'name' => 'XI IPA 2'],
            ['id' => 6, 'name' => 'XI IPS 1'],
            ['id' => 7, 'name' => 'XII IPA 1'],
            ['id' => 8, 'name' => 'XII IPA 2'],
            ['id' => 9, 'name' => 'XII IPS 1'],
        ];

        // Create demo teachers
        $teachers = [
            ['id' => 1, 'name' => 'Budi Santoso', 'nip' => '198501012010011001'],
            ['id' => 2, 'name' => 'Siti Rahayu', 'nip' => '198601022010012002'],
            ['id' => 3, 'name' => 'Ahmad Hidayat', 'nip' => '198702032010011003'],
            ['id' => 4, 'name' => 'Dewi Lestari', 'nip' => '198803042010012004'],
            ['id' => 5, 'name' => 'Eko Prasetyo', 'nip' => '198904052010011005'],
            ['id' => 6, 'name' => 'Fitriani', 'nip' => '199005062010012006'],
            ['id' => 7, 'name' => 'Gunawan', 'nip' => '199106072010011007'],
            ['id' => 8, 'name' => 'Heni Wulandari', 'nip' => '199207082010012008'],
            ['id' => 9, 'name' => 'Irfan Maulana', 'nip' => '199308092010011009'],
            ['id' => 10, 'name' => 'Juwita Sari', 'nip' => '199409102010012010'],
        ];

        // Only use lesson hours that are not breaks
        $teachingHours = $lessonHours->filter(fn ($hour) => ! str_contains(strtolower($hour->name), 'istirahat'));

        // Create schedules for each teaching hour
        $scheduleId = 1;
        foreach ($teachingHours as $hour) {
            // For jam ke-1, create more schedules to show variety
            $numSchedules = ($hour->sequence == 1) ? 6 : 3;

            // Create different schedules for each hour (different classrooms)
            for ($i = 0; $i < $numSchedules; $i++) {
                $schedule = new stdClass;
                $schedule->id = $scheduleId++;
                $schedule->lesson_hour_id = $hour->id;
                $schedule->day_of_week = $dayOfWeek;
                $schedule->status = 'active';

                // Create teacher assignment
                $teacherAssignment = new stdClass;
                $teacherAssignment->id = $scheduleId; // Use same ID for simplicity

                // Randomly select subject, classroom, and teacher
                $subjectIndex = array_rand($subjects);
                $classroomIndex = array_rand($classrooms);
                $teacherIndex = array_rand($teachers);

                // Create subject
                $subject = new stdClass;
                $subject->id = $subjects[$subjectIndex]['id'];
                $subject->name = $subjects[$subjectIndex]['name'];

                // Create classroom
                $classroom = new stdClass;
                $classroom->id = $classrooms[$classroomIndex]['id'];
                $classroom->name = $classrooms[$classroomIndex]['name'];

                // Create teacher with user
                $teacher = new stdClass;
                $teacher->id = $teachers[$teacherIndex]['id'];
                $teacher->nip = $teachers[$teacherIndex]['nip'];

                $user = new stdClass;
                $user->id = $teachers[$teacherIndex]['id'];
                $user->name = $teachers[$teacherIndex]['name'];

                $teacher->user = $user;

                // Assign to teacher assignment
                $teacherAssignment->subject = $subject;
                $teacherAssignment->classroom = $classroom;
                $teacherAssignment->teacher = $teacher;
                $teacherAssignment->teacher_id = $teacher->id;

                // Assign lesson hour
                $schedule->lessonHour = $hour;

                // Assign teacher assignment to schedule
                $schedule->teacherAssignment = $teacherAssignment;

                $schedules->push($schedule);
            }
        }

        return $schedules;
    }

    /**
     * Create demo attendances for display purposes
     */
    private function createDemoAttendances(Collection $schedules): Collection
    {
        // Group attendances by schedule ID
        $groupedAttendances = collect();

        foreach ($schedules as $schedule) {
            $scheduleAttendances = collect();

            // Create teacher attendance with random status
            $teacherAttendance = new stdClass;
            $teacherAttendance->id = $schedule->id * 100;
            $teacherAttendance->class_schedule_id = $schedule->id;
            $teacherAttendance->teacher_id = $schedule->teacherAssignment->teacher_id;
            $teacherAttendance->student_id = null;
            $teacherAttendance->attendance_date = Carbon::now()->format('Y-m-d H:i:s');

            // For jam ke-1, ensure we have all types of attendance status for demo
            if ($schedule->lessonHour->sequence == 1) {
                // Distribute attendance status evenly for jam ke-1
                $index = $schedule->id % 4;
                if ($index == 0) {
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::PRESENT;
                } elseif ($index == 1) {
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::LATE;
                } elseif ($index == 2) {
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::ABSENT;
                } else {
                    $teacherAttendance->attendance_status = null; // Unknown status
                }
            } else {
                // For other hours, randomly assign attendance status with weighted probability
                $rand = mt_rand(1, 100);
                if ($rand <= 70) {
                    // 70% chance of present
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::PRESENT;
                } elseif ($rand <= 85) {
                    // 15% chance of late
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::LATE;
                } else {
                    // 15% chance of absent
                    $teacherAttendance->attendance_status = AttendanceStatusEnum::ABSENT;
                }
            }

            $teacherAttendance->attendant_type = AttendantTypeEnum::TEACHER;
            $teacherAttendance->notes = '';

            $scheduleAttendances->push($teacherAttendance);

            // Add to grouped attendances
            $groupedAttendances->put($schedule->id, $scheduleAttendances);
        }

        return $groupedAttendances;
    }
}

<?php

namespace App\Http\Requests\LessonHourRequests;

use Illuminate\Foundation\Http\FormRequest;

class LessonHourFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'classroom_id' => 'nullable|integer|exists:classrooms,id',
            'shift_id' => 'nullable|integer|exists:shifts,id',
            'search' => 'nullable|string|max:255',
        ];
    }
}

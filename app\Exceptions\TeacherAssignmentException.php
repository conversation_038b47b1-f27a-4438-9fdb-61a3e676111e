<?php

namespace App\Exceptions;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class TeacherAssignmentException extends Exception
{
    /**
     * @var int
     */
    protected $statusCode = Response::HTTP_BAD_REQUEST;

    /**
     * @var array
     */
    protected $errors = [];

    /**
     * @var array
     */
    protected $headers = [];

    /**
     * TeacherAssignmentException constructor.
     */
    public function __construct(
        string $message = 'Terjadi kesalahan pada penugasan guru',
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        if ($statusCode !== null) {
            $this->statusCode = $statusCode;
        }

        $this->errors = $errors;
        $this->headers = $headers;
    }

    /**
     * Get the HTTP status code.
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the errors array.
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the headers array.
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Create a duplicate assignment exception.
     */
    public static function duplicateAssignment(string $message = 'Guru sudah ditugaskan untuk mata pelajaran dan kelas yang sama'): self
    {
        return new self($message, Response::HTTP_CONFLICT);
    }

    /**
     * Create a homeroom teacher conflict exception.
     */
    public static function homeroomTeacherConflict(string $message = 'Kelas ini sudah memiliki wali kelas'): self
    {
        return new self($message, Response::HTTP_CONFLICT);
    }

    /**
     * Create a validation exception.
     */
    public static function validationError(string $message = 'Data penugasan guru tidak valid'): self
    {
        return new self($message, Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Create a not found exception.
     */
    public static function notFound(string $message = 'Penugasan guru tidak ditemukan'): self
    {
        return new self($message, Response::HTTP_NOT_FOUND);
    }

    /**
     * Create a cannot delete exception.
     */
    public static function cannotDelete(string $message = 'Penugasan guru tidak dapat dihapus karena masih digunakan'): self
    {
        return new self($message, Response::HTTP_CONFLICT);
    }

    /**
     * Create a teacher qualification exception.
     */
    public static function teacherNotQualified(string $message = 'Guru tidak memiliki kualifikasi untuk mata pelajaran ini'): self
    {
        return new self($message, Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
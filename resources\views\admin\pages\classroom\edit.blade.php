@extends('admin.layouts.app')

@section('title', '<PERSON> Kelas')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Kelas',
        'breadcrumb' => 'Manajemen Sekolah',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0">Form Edit Data Kelas</h5>
                        </div>
                        <div>
                            <a href="{{ route('admin.classrooms.index') }}" class="btn btn-ghost-info">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form id="editClassroomForm" action="{{ route('admin.classrooms.update', $classroom->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Dasar</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Kelas <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   placeholder="Masukkan nama kelas" value="{{ old('name', $classroom->name) }}" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="level" class="form-label">Level <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="level" name="level" required>
                                                <option value="">Pilih Level</option>
                                                @foreach ($levels as $value => $label)
                                                    <option value="{{ $value }}" {{ old('level', $classroom->level?->value) == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="level"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="program_id" class="form-label">Program <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="program_id" name="program_id" required>
                                                <option value="">Pilih Program</option>
                                                @foreach ($programs as $program)
                                                    <option value="{{ $program->id }}" {{ old('program_id', $classroom->program_id) == $program->id ? 'selected' : '' }}>
                                                        {{ $program->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="program_id"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="teacher_id" class="form-label">Wali Kelas <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="teacher_id" name="teacher_id" required>
                                                <option value="">Pilih Wali Kelas</option>
                                                @foreach ($teachers as $teacher)
                                                    <option value="{{ $teacher->id }}" {{ old('teacher_id', $classroom->teacher_id) == $teacher->id ? 'selected' : '' }}>
                                                        {{ $teacher->user->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="teacher_id"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Tambahan</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                                <option value="">Pilih Tahun Akademik</option>
                                                @foreach ($academicYears as $academicYear)
                                                    <option value="{{ $academicYear->id }}" {{ old('academic_year_id', $classroom->academic_year_id) == $academicYear->id ? 'selected' : '' }}>
                                                        {{ $academicYear->formatted_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="academic_year_id"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="capacity" name="capacity"
                                                   placeholder="Masukkan kapasitas kelas" min="1" value="{{ old('capacity', $classroom->capacity) }}" required>
                                            <div class="invalid-feedback" data-field="capacity"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="shift_id" class="form-label">Shift</label>
                                            <select class="form-select" data-choices id="shift_id" name="shift_id">
                                                <option value="">Pilih Shift (Opsional)</option>
                                                @foreach ($shifts as $shift)
                                                    <option value="{{ $shift->id }}" {{ old('shift_id', $classroom->shift_id) == $shift->id ? 'selected' : '' }}>
                                                        {{ $shift->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="shift_id"></div>
                                            <div class="form-text">Shift menentukan jadwal jam pelajaran yang akan digunakan.</div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                @foreach ($statuses as $value => $label)
                                                    <option value="{{ $value }}" {{ old('status', $classroom->status?->value) === $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="description" class="form-label">Deskripsi</label>
                                            <textarea class="form-control" id="description" name="description"
                                                      rows="3" placeholder="Masukkan deskripsi kelas (opsional)">{{ old('description', $classroom->description) }}</textarea>
                                            <div class="invalid-feedback" data-field="description"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.classrooms.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan Perubahan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            $('#editClassroomForm').on('submit', function(e) {
                e.preventDefault();

                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                const $submitBtn = $('#submit-btn');
                const originalText = $submitBtn.html();
                $submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...').prop('disabled', true);

                const formData = new FormData(this);

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "{{ route('admin.classrooms.index') }}";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            $.each(errors, function(field, messages) {
                                const $input = $(`[name="${field}"]`);
                                $input.addClass('is-invalid');
                                $(`[data-field="${field}"]`).text(messages[0]);
                            });
                        } else {
                            let message = 'Terjadi kesalahan saat menyimpan data';
                            if (xhr.status === 403) {
                                message = 'Anda tidak memiliki izin untuk melakukan aksi ini';
                            } else if (xhr.status === 404) {
                                message = 'Data tidak ditemukan';
                            } else if (xhr.responseJSON?.message) {
                                message = xhr.responseJSON.message;
                            }
                            Swal.fire({
                                title: 'Error!',
                                text: message,
                                icon: 'error'
                            });
                        }
                    },
                    complete: function() {
                        $submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

<?php

namespace App\Http\Requests\LessonHourRequests;

use Illuminate\Foundation\Http\FormRequest;

class LessonHourStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'sequence' => 'required|integer|min:1',
            'classroom_id' => 'nullable|integer|exists:classrooms,id',
            'shift_id' => 'nullable|integer|exists:shifts,id',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Nama jam pelajaran wajib diisi',
            'name.max' => 'Nama jam pelajaran maksimal 255 karakter',
            'start_time.required' => 'Waktu mulai wajib diisi',
            'start_time.date_format' => 'Format waktu mulai tidak valid (HH:MM)',
            'end_time.required' => 'Waktu selesai wajib diisi',
            'end_time.date_format' => 'Format waktu selesai tidak valid (HH:MM)',
            'end_time.after' => 'Waktu selesai harus setelah waktu mulai',
            'sequence.required' => 'Urutan wajib diisi',
            'sequence.integer' => 'Urutan harus berupa angka',
            'sequence.min' => 'Urutan minimal 1',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid',
        ];
    }
}

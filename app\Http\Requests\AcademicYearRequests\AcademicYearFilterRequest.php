<?php

namespace App\Http\Requests\AcademicYearRequests;

use Illuminate\Validation\Rule;
use App\Enums\AcademicSemesterEnum;
use App\Enums\AcademicYearStatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class AcademicYearFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => ['sometimes', 'nullable', 'string', Rule::in(AcademicYearStatusEnum::values())],
            'semester' => ['sometimes', 'nullable', 'string', Rule::in(AcademicSemesterEnum::values())],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status' => 'Status',
            'semester' => 'Semester',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Handle empty string as null for proper filter application
        $this->merge([
            'status' => $this->status === '' ? null : $this->status,
            'semester' => $this->semester === '' ? null : $this->semester,
            'search' => $this->search === '' ? null : $this->search,
        ]);
    }
}

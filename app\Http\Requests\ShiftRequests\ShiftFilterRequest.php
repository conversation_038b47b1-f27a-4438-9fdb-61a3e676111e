<?php

namespace App\Http\Requests\ShiftRequests;

use App\Enums\ShiftStatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ShiftFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'status' => 'nullable|in:' . implode(',', ShiftStatusEnum::values()),
            'search' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'status' => 'Status',
            'search' => 'Pencarian',
        ];
    }
}

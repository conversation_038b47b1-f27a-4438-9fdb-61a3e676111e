<?php

namespace App\Enums;

enum AcademicSemesterEnum: string
{
    case ODD = 'odd';
    case EVEN = 'even';

    /**
     * Get label for the enum instance
     */
    public function label(): string
    {
        return match ($this) {
            self::ODD => 'Ganjil',
            self::EVEN => 'Genap',
        };
    }

    /**
     * Get all options as array [label] (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::ODD->value => self::ODD->label(),
            self::EVEN->value => self::EVEN->label(),
        ];
    }

    /**
     * Static access to label by string or enum
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            return $value->label();
        }

        return self::tryFrom($value)?->label() ?? $value;
    }
    public static function values(): array
    {
        return array_map(fn(self $case) => $case->value, self::cases());
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->string('code')->nullable()->after('name');
            $table->enum('status', ['active', 'inactive'])->default('active')->after('description');
        });

        // Update existing records with unique codes
        DB::table('programs')->whereNull('code')->get()->each(function ($program) {
            DB::table('programs')
                ->where('id', $program->id)
                ->update(['code' => 'PROG-'.$program->id]);
        });

        // Add unique constraint
        Schema::table('programs', function (Blueprint $table) {
            $table->string('code')->unique()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->dropColumn(['code', 'status']);
        });
    }
};

<?php

namespace App\Enums;

enum LeaveStatusEnum: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    /**
     * Get all leave status values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all leave statuses as key-value pairs (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::PENDING->value => 'Menunggu Persetujuan',
            self::APPROVED->value => 'Disetujui',
            self::REJECTED->value => 'Ditolak',
        ];
    }

    /**
     * Get the display label for a leave status value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get color class for the leave status
     */
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::APPROVED => 'success',
            self::REJECTED => 'danger',
        };
    }

    /**
     * Get icon for the leave status
     */
    public function icon(): string
    {
        return match ($this) {
            self::PENDING => 'ri-time-line',
            self::APPROVED => 'ri-check-line',
            self::REJECTED => 'ri-close-line',
        };
    }

    /**
     * Check if status allows modification
     */
    public function allowsModification(): bool
    {
        return $this === self::PENDING;
    }

    /**
     * Check if status allows deletion
     */
    public function allowsDeletion(): bool
    {
        return $this !== self::APPROVED;
    }
}
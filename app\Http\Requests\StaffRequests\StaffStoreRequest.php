<?php

namespace App\Http\Requests\StaffRequests;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;

class StaffStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'max:100', 'unique:users,username', 'alpha_dash'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'string', Password::defaults()],
            'phone_number' => ['required', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/', 'unique:users,phone_number'],
            'role' => ['required', 'string', Rule::in($this->getAllowedRoles())],
            'status' => ['required', Rule::enum(UserStatus::class)],
        ];
    }

    /**
     * Get allowed roles for staff creation.
     */
    public function getAllowedRoles(): array
    {
        return RoleEnum::staff();
    }

    /**
     * Get allowed statuses for staff creation.
     */
    public function getAllowedStatuses(): array
    {
        return UserStatus::values();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'phone_number' => 'Nomor Telepon',
            'role' => 'Role',
            'status' => 'Status',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama wajib diisi.',
            'name.string' => 'Nama harus berupa teks.',
            'name.max' => 'Nama tidak boleh lebih dari 255 karakter.',
            'username.required' => 'Username wajib diisi.',
            'username.string' => 'Username harus berupa teks.',
            'username.max' => 'Username tidak boleh lebih dari 100 karakter.',
            'username.unique' => 'Username sudah digunakan.',
            'username.alpha_dash' => 'Username hanya boleh berisi huruf, angka, tanda hubung, dan garis bawah.',
            'email.required' => 'Email wajib diisi.',
            'email.string' => 'Email harus berupa teks.',
            'email.email' => 'Email harus berupa alamat email yang valid.',
            'email.max' => 'Email tidak boleh lebih dari 255 karakter.',
            'email.unique' => 'Email sudah digunakan.',
            'password.required' => 'Password wajib diisi.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password harus memiliki panjang minimal :min karakter.',
            'phone_number.required' => 'Nomor telepon wajib diisi.',
            'phone_number.string' => 'Nomor telepon harus berupa teks.',
            'phone_number.max' => 'Nomor telepon tidak boleh lebih dari 20 karakter.',
            'phone_number.regex' => 'Nomor telepon hanya boleh berisi angka, tanda plus, tanda hubung, spasi, atau tanda kurung.',
            'phone_number.unique' => 'Nomor telepon sudah digunakan.',
            'role.required' => 'Role wajib dipilih.',
            'role.string' => 'Role harus berupa teks.',
            'role.in' => 'Role yang dipilih tidak valid untuk staf.',
            'status.required' => 'Status wajib dipilih.',
            'status.enum' => 'Status yang dipilih tidak valid.',
        ];
    }
}

<?php

namespace App\Http\Requests\LeaveRequests;

use App\Enums\LeaveStatusEnum;
use App\Enums\LeaveTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
            'leave_type' => ['required', 'string', Rule::in(LeaveTypeEnum::values())],
            'leave_date' => ['required', 'date'],
            'reason' => ['required', 'string', 'max:500'],
            'attachment_path' => ['nullable', 'string', 'max:255'],
            'status' => ['sometimes', 'string', Rule::in(LeaveStatusEnum::values())],
            'approved_by' => ['nullable', 'integer', 'exists:users,id'],
            'tasks' => ['sometimes', 'array'],
            'tasks.*.id' => ['sometimes', 'integer', 'exists:leave_tasks,id'],
            'tasks.*.task_description' => ['required_with:tasks', 'string', 'max:1000'],
            'tasks.*.attachment_path' => ['nullable', 'string', 'max:255'],
            'deleted_tasks' => ['sometimes', 'array'],
            'deleted_tasks.*' => ['required_with:deleted_tasks', 'integer', 'exists:leave_tasks,id'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'teacher_id.required' => 'Guru harus dipilih.',
            'teacher_id.exists' => 'Guru yang dipilih tidak valid.',
            'leave_type.required' => 'Jenis cuti harus dipilih.',
            'leave_type.in' => 'Jenis cuti yang dipilih tidak valid.',
            'leave_date.required' => 'Tanggal cuti harus diisi.',
            'leave_date.date' => 'Format tanggal cuti tidak valid.',
            'reason.required' => 'Alasan cuti harus diisi.',
            'reason.max' => 'Alasan cuti maksimal 500 karakter.',
            'attachment_path.max' => 'Path file lampiran terlalu panjang.',
            'status.in' => 'Status yang dipilih tidak valid.',
            'approved_by.exists' => 'Approver yang dipilih tidak valid.',
            'tasks.*.task_description.required_with' => 'Deskripsi tugas harus diisi.',
            'tasks.*.task_description.max' => 'Deskripsi tugas maksimal 1000 karakter.',
            'tasks.*.attachment_path.max' => 'Path file lampiran tugas terlalu panjang.',
            'tasks.*.id.exists' => 'Tugas yang dipilih tidak valid.',
            'deleted_tasks.*.exists' => 'Tugas yang akan dihapus tidak valid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'guru',
            'leave_type' => 'jenis cuti',
            'leave_date' => 'tanggal cuti',
            'reason' => 'alasan cuti',
            'attachment_path' => 'file lampiran',
            'status' => 'status',
            'approved_by' => 'approver',
            'tasks' => 'daftar tugas',
            'tasks.*.task_description' => 'deskripsi tugas',
            'tasks.*.attachment_path' => 'file lampiran tugas',
            'deleted_tasks' => 'tugas yang dihapus',
        ];
    }
}

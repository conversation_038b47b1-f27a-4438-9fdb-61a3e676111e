<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AcademicSemesterEnum;
use App\Enums\AcademicYearStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\AcademicYearRequests\AcademicYearFilterRequest;
use App\Http\Requests\AcademicYearRequests\AcademicYearStoreRequest;
use App\Http\Requests\AcademicYearRequests\AcademicYearUpdateRequest;
use App\Models\AcademicYear;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Throwable;

class AcademicYearController extends Controller
{
    /**
     * Display a listing of the academic years.
     */
    public function index(AcademicYearFilterRequest $request): View|JsonResponse
    {
        $query = AcademicYear::query();

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatAcademicYearsForDatatable($query);
        }

        return view('admin.pages.academic-year.index', [
            'statuses' => AcademicYearStatusEnum::options(),
            'semesters' => AcademicSemesterEnum::options(),
        ]);
    }

    /**
     * Apply filters to the query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Filter by semester
        if (!empty($filters['semester'])) {
            $query->where('semester', $filters['semester']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query;
    }

    /**
     * Format academic years for DataTable
     */
    protected function formatAcademicYearsForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name)
            ->editColumn('semester', fn($row) => $row->semester?->label())
            ->editColumn('start_date', fn($row) => $row->start_date->format('d/m/Y'))
            ->editColumn('end_date', fn($row) => $row->end_date->format('d/m/Y'))
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.academic-year._action', [
                    'id' => $row->id,
                    'edit' => route('admin.academic-years.edit', $row->id),
                    'destroy' => route('admin.academic-years.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['semester', 'status', 'action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new academic year.
     */
    public function create(): View
    {
        return view('admin.pages.academic-year.create', [
            'semesters' => AcademicSemesterEnum::options(),
            'statuses' => AcademicYearStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created academic year
     */
    public function store(AcademicYearStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $academicYear = AcademicYear::create([
                'name' => $validated['name'],
                'semester' => $validated['semester'],
                'start_date' => $validated['start_date'],
                'end_date' => $validated['end_date'],
                'status' => $validated['status'],
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tahun akademik berhasil dibuat.',
                'data' => $academicYear,
            ], Response::HTTP_CREATED);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat tahun akademik.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified academic year.
     */
    public function show(AcademicYear $academicYear)
    {
        //
    }

    /**
     * Show the form for editing the specified academic year.
     */
    public function edit(int $id): View
    {
        $academicYear = AcademicYear::findOrFail($id);

        return view('admin.pages.academic-year.edit', [
            'academicYear' => $academicYear,
            'semesters' => AcademicSemesterEnum::options(),
            'statuses' => AcademicYearStatusEnum::options(),
        ]);
    }

    /**
     * Update the specified academic year
     */
    public function update(AcademicYearUpdateRequest $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $academicYear = AcademicYear::findOrFail($id);
            $validated = $request->validated();

            $academicYear->update([
                'name' => $validated['name'],
                'semester' => $validated['semester'],
                'start_date' => $validated['start_date'],
                'end_date' => $validated['end_date'],
                'status' => $validated['status'],
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tahun akademik berhasil diperbarui.',
                'data' => $academicYear,
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui tahun akademik.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified academic year
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $academicYear = AcademicYear::findOrFail($id);

            // Check if the academic year has related data before deleting
            $hasClassrooms = $academicYear->classrooms()->exists();
            $hasTeacherAssignments = $academicYear->teacherAssignments()->exists();
            $hasClassroomStudents = $academicYear->classroomStudents()->exists();
            $hasAttendances = $academicYear->attendances()->exists();

            if ($hasClassrooms || $hasTeacherAssignments || $hasClassroomStudents || $hasAttendances) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tahun akademik tidak dapat dihapus karena masih memiliki data terkait.',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $academicYear->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tahun akademik berhasil dihapus.',
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus tahun akademik.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Program;
use App\Models\Subject;
use Illuminate\Database\Seeder;

class SubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $programs = Program::all();

        // Common subjects for all programs
        $commonSubjects = [
            'Pendidikan Agama Islam',
            '<PERSON>an Had<PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Sejarah Kebudayaan Islam',
            'Bahasa Arab',
            'Pendidikan Pancasila',
            'Bahasa Indonesia',
            'Matematika',
            'Ilmu Pengetahuan Alam',
            '<PERSON><PERSON> Pengetahuan Sosial',
            'Bahasa Inggris',
            '<PERSON>didi<PERSON>, Olah<PERSON> dan <PERSON>',
            'Informatika',
            'Seni Budaya / Prakarya',
            'Muatan Lokal',
            'Bahasa Jawa',
            'Aswaja',
            'Nahwu',
            'Bimbingan Membaca Kitab',
            'Tahfidz',
        ];

        // Program-specific subjects
        $programSubjects = [
            'UNG' => [
                'Pendidikan Agama Islam',
                '<PERSON>',
                '<PERSON><PERSON><PERSON>',
                '<PERSON><PERSON><PERSON>',
                '<PERSON><PERSON><PERSON>',
                'Bahasa Arab',
                'Pendidikan Pancasila',
                'Bahasa Indonesia',
                'Matematika',
                '<PERSON><PERSON> Pengetahuan Alam',
                'Ilmu Pengetahuan Sosial',
                'Bahasa Inggris',
                'Pendidikan Jasmani, Olahraga dan Kesehatan',
                'Informatika',
                'Seni Budaya / Prakarya',
                'Muatan Lokal',
                'Bahasa Jawa',
                'Aswaja',
                'Nahwu',
                'Bimbingan Membaca Kitab',
                'Tahfidz',
            ],
            'REG' => [
                'Pendidikan Agama Islam',
                'Al Qur`an Hadits',
                'Akidah Akhlak',
                'Fikih',
                'Sejarah Kebudayaan Islam',
                'Bahasa Arab',
                'Pendidikan Pancasila',
                'Bahasa Indonesia',
                'Matematika',
                'Ilmu Pengetahuan Alam',
                'Ilmu Pengetahuan Sosial',
                'Bahasa Inggris',
                'Pendidikan Jasmani, Olahraga dan Kesehatan',
                'Informatika',
                'Seni Budaya / Prakarya',
                'Muatan Lokal',
                'Bahasa Jawa',
                'Aswaja',
                'Nahwu',
                'Bimbingan Membaca Kitab',
                'Tahfidz',
            ],
        ];

        foreach ($programs as $program) {
            // Add common subjects for all programs
            foreach ($commonSubjects as $subject) {
                Subject::create([
                    'name' => $subject,
                    'program_id' => $program->id,
                ]);
            }
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'program_id',
    ];

    /**
     * Get the program that owns the subject.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the teacher assignments for the subject.
     */
    public function teacherAssignments(): HasM<PERSON>
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}

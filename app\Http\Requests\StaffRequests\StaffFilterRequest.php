<?php

namespace App\Http\Requests\StaffRequests;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;

class StaffFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'status' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedStatuses())],
            'role' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedRoles())],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get allowed roles for staff filtering.
     */
    public function getAllowedRoles(): array
    {
        return RoleEnum::staff();
    }

    /**
     * Get allowed statuses for staff filtering.
     */
    public function getAllowedStatuses(): array
    {
        return UserStatus::values();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status' => 'Status',
            'role' => 'Role',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'role.in' => 'Role yang dipilih tidak valid untuk staf.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }
}

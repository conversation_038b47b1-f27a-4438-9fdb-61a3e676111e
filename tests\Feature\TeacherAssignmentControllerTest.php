<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Teacher;
use App\Models\Subject;
use App\Models\Classroom;
use App\Models\Program;
use App\Models\AcademicYear;
use App\Models\TeacherAssignment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TeacherAssignmentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $teacher;
    protected $program;
    protected $subject;
    protected $classroom;
    protected $academicYear;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        // Create test data
        $this->program = Program::factory()->create(['status' => 'active']);
        $this->subject = Subject::factory()->create(['program_id' => $this->program->id]);
        $this->academicYear = AcademicYear::factory()->create(['status' => 'active']);
        $this->classroom = Classroom::factory()->create([
            'program_id' => $this->program->id,
            'academic_year_id' => $this->academicYear->id,
            'status' => 'active'
        ]);

        // Create teacher
        $teacherUser = User::factory()->create(['status' => 'active']);
        $teacherUser->assignRole('subject_teacher');
        $this->teacher = Teacher::factory()->create(['user_id' => $teacherUser->id]);
    }

    public function test_index_returns_view_with_optimized_queries()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.teacher-assignments.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.pages.teacher-assignment.index');
        $response->assertViewHas(['teachers', 'subjects', 'classrooms', 'programs', 'academicYears']);
    }

    public function test_index_returns_datatable_data()
    {
        $this->actingAs($this->admin);

        // Create test assignment
        TeacherAssignment::factory()->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
        ]);

        $response = $this->get(route('admin.teacher-assignments.index'), [
            'HTTP_X-Requested-With' => 'XMLHttpRequest'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'teacher_name',
                    'subject_name',
                    'classroom_name',
                    'academic_year_name'
                ]
            ]
        ]);
    }

    public function test_store_creates_teacher_assignment_with_validation()
    {
        $this->actingAs($this->admin);

        $data = [
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => false,
        ];

        $response = $this->postJson(route('admin.teacher-assignments.store'), $data);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'teacher',
                'subject',
                'classroom',
                'academic_year'
            ]
        ]);

        $this->assertDatabaseHas('teacher_assignments', [
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
        ]);
    }

    public function test_store_prevents_duplicate_assignment()
    {
        $this->actingAs($this->admin);

        // Create first assignment
        TeacherAssignment::factory()->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
        ]);

        // Try to create duplicate
        $data = [
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => false,
        ];

        $response = $this->postJson(route('admin.teacher-assignments.store'), $data);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['subject_id']);
    }

    public function test_store_prevents_multiple_homeroom_teachers()
    {
        $this->actingAs($this->admin);

        // Create homeroom teacher assignment
        TeacherAssignment::factory()->create([
            'teacher_id' => $this->teacher->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => true,
        ]);

        // Create another teacher
        $anotherTeacherUser = User::factory()->create(['status' => 'active']);
        $anotherTeacherUser->assignRole('subject_teacher');
        $anotherTeacher = Teacher::factory()->create(['user_id' => $anotherTeacherUser->id]);

        // Try to assign another homeroom teacher to same classroom
        $data = [
            'teacher_id' => $anotherTeacher->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => true,
        ];

        $response = $this->postJson(route('admin.teacher-assignments.store'), $data);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['is_homeroom_teacher']);
    }

    public function test_get_statistics_returns_correct_data()
    {
        $this->actingAs($this->admin);

        // Create test assignments
        TeacherAssignment::factory()->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'classroom_id' => $this->classroom->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => true,
        ]);

        $response = $this->getJson(route('admin.teacher-assignments.statistics'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_assignments',
            'homeroom_teachers',
            'subject_teachers',
            'active_assignments',
            'assignments_by_program',
            'assignments_by_academic_year'
        ]);
    }

    public function test_check_assignment_availability()
    {
        $this->actingAs($this->admin);

        $response = $this->getJson(route('admin.teacher-assignments.check-availability'), [
            'teacher_id' => $this->teacher->id,
            'classroom_id' => $this->classroom->id,
            'subject_id' => $this->subject->id,
            'academic_year_id' => $this->academicYear->id,
            'is_homeroom_teacher' => false,
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'available',
            'conflicts'
        ]);
    }
}

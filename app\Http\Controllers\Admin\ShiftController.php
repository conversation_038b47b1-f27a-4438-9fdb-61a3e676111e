<?php

namespace App\Http\Controllers\Admin;

use App\Models\Shift;
use App\Enums\ShiftStatusEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\ShiftRequests\ShiftStoreRequest;
use App\Http\Requests\ShiftRequests\ShiftFilterRequest;
use App\Http\Requests\ShiftRequests\ShiftUpdateRequest;

class ShiftController extends Controller
{
    /**
     * Display a listing of shifts.
     */
    public function index(ShiftFilterRequest $request): View|JsonResponse
    {
        $query = Shift::select(['id', 'name', 'description', 'status', 'created_at']);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatShiftForDatatable($query);
        }

        return view('admin.pages.shift.index', [
            'statuses' => ShiftStatusEnum::dropdown(),
            'initialFilters' => $request->validated(), // Pass filters to view
        ]);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('description', 'like', $searchTerm);
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables.
     */
    protected function formatShiftForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name ?? '-')
            ->editColumn('description', fn($row) => $row->description ?? '-')
            ->editColumn('status', function ($row) {
                // Since the model uses enum casting, $row->status is already an enum object
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.shift._action', [
                    'edit' => route('admin.shifts.edit', $row->id),
                    'destroy' => route('admin.shifts.destroy', $row->id),
                    'id' => $row->id,
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new shift.
     */
    public function create(): View
    {
        return view('admin.pages.shift.create', [
            'statuses' => ShiftStatusEnum::dropdown(),
        ]);
    }

    /**
     * Store a newly created shift.
     */
    public function store(ShiftStoreRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $shift = Shift::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'status' => $validated['status'],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Data shift berhasil dibuat.',
            'data' => $shift,
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the form for editing the specified shift.
     */
    public function edit(int $id): View
    {
        $shift = Shift::findOrFail($id);

        return view('admin.pages.shift.edit', [
            'shift' => $shift,
            'statuses' => ShiftStatusEnum::dropdown(),
        ]);
    }

    /**
     * Update the specified shift.
     */
    public function update(ShiftUpdateRequest $request, int $id): JsonResponse
    {
        $shift = Shift::findOrFail($id);
        $validated = $request->validated();

        $updateData = [
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'status' => $validated['status'],
        ];

        $shift->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Data shift berhasil diperbarui.',
        ]);
    }

    /**
     * Remove the specified shift.
     */
    public function destroy(int $id): JsonResponse
    {
        $shift = Shift::findOrFail($id);

        // Check if shift has related data
        if ($shift->classrooms()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus shift yang memiliki kelas terkait.',
            ], Response::HTTP_FORBIDDEN);
        }

        if ($shift->lessonHours()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus shift yang memiliki jam pelajaran terkait.',
            ], Response::HTTP_FORBIDDEN);
        }

        $shift->delete();

        return response()->json([
            'success' => true,
            'message' => 'Data shift berhasil dihapus.',
        ]);
    }
}

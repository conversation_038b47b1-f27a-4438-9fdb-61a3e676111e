<?php

namespace App\Exports;

use App\Enums\GenderEnum;
use App\Enums\ReligionEnum;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentTemplateExport implements FromArray, WithHeadings, WithStyles
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Sample data for the template - empty row for user to fill
        return [
            [
                'nis' => '',
                'nisn' => '',
                'nama' => '',
                'email' => '',
                'jenis_kelamin' => '',
                'tempat_lahir' => '',
                'tanggal_lahir' => '',
                'agama' => '',
                'alamat' => '',
                'telepon' => '',
                'nama_ayah' => '',
                'nama_ibu' => '',
                'telepon_ortu' => '',
            ],
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'NIS',
            'NISN',
            'Nama Si<PERSON>wa',
            'Email',
            'Jenis Kelamin (Laki-laki/Perempuan)',
            'Tempat Lahir',
            'Tanggal Lahir (DD/MM/YYYY)',
            'Agama (' . implode('/', ReligionEnum::labels()) . ')',
            'Alamat',
            'Telepon',
            'Nama Ayah',
            'Nama Ibu',
            'Telepon Orangtua',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:M1')->getFont()->setBold(true);

        // Add comments for guidance
        $sheet->getComment('E2')->getText()->createTextRun('Pilih: Laki-laki atau Perempuan');
        $sheet->getComment('G2')->getText()->createTextRun('Format: DD/MM/YYYY, misal 01/01/2000');
        $sheet->getComment('H2')->getText()->createTextRun('Pilih: ' . implode(', ', ReligionEnum::labels()));

        // Add data validation for Gender
        $validation = $sheet->getCell('E2')->getDataValidation();
        $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST)
            ->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION)
            ->setAllowBlank(false)
            ->setShowInputMessage(true)
            ->setShowErrorMessage(true)
            ->setShowDropDown(true)
            ->setErrorTitle('Pilihan tidak valid')
            ->setError('Pilih dari daftar yang disediakan')
            ->setPromptTitle('Pilih jenis kelamin')
            ->setPrompt('Laki-laki atau Perempuan')
            ->setFormula1('"Laki-laki,Perempuan"');

        // Add data validation for Religion
        $validation = $sheet->getCell('H2')->getDataValidation();
        $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST)
            ->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION)
            ->setAllowBlank(false)
            ->setShowInputMessage(true)
            ->setShowErrorMessage(true)
            ->setShowDropDown(true)
            ->setErrorTitle('Pilihan tidak valid')
            ->setError('Pilih dari daftar yang disediakan')
            ->setPromptTitle('Pilih agama')
            ->setPrompt('Pilih agama dari daftar')
            ->setFormula1('"' . implode(',', ReligionEnum::labels()) . '"');

        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

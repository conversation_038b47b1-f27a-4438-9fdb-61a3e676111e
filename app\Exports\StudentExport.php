<?php

namespace App\Exports;

use App\Enums\GenderEnum;
use App\Enums\ReligionEnum;
use App\Models\Student;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $query = Student::with(['user']);

        // Apply filters if any
        if (isset($this->filters['classroom_id'])) {
            $query->whereHas('classrooms', function ($q) {
                $q->where('classrooms.id', $this->filters['classroom_id']);
            });
        }

        if (isset($this->filters['status'])) {
            $query->whereHas('user', function ($q) {
                $q->where('status', $this->filters['status']);
            });
        }

        return $query->get();
    }

    /**
     * @param Student $student
     * @return array
     */
    public function map($student): array
    {
        return [
            $student->nis,
            $student->nisn,
            $student->user->name,
            $student->user->email,
            GenderEnum::getLabel($student->gender),
            $student->birth_place,
            $student->birth_date ? date('d/m/Y', strtotime($student->birth_date)) : '',
            ReligionEnum::getLabel($student->religion),
            $student->address,
            $student->phone,
            $student->father_name,
            $student->mother_name,
            $student->parent_phone,
            $student->user->status->label(),
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'NIS',
            'NISN',
            'Nama Siswa',
            'Email',
            'Jenis Kelamin',
            'Tempat Lahir',
            'Tanggal Lahir',
            'Agama',
            'Alamat',
            'Telepon',
            'Nama Ayah',
            'Nama Ibu',
            'Telepon Orangtua',
            'Status',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

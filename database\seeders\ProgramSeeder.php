<?php

namespace Database\Seeders;

use App\Models\Program;
use Illuminate\Database\Seeder;

class ProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $programs = [
            [
                'name' => 'Unggulan',
                'code' => 'UNG',
                'description' => 'Program Unggulan untuk siswa dengan prestasi akademik tinggiss',
                'status' => 'active',
            ],
            [
                'name' => 'Reguler',
                'code' => 'REG',
                'description' => 'Program Reguler untuk siswa dengan prestasi akademik rata-rata',
                'status' => 'active',
            ],
        ];

        foreach ($programs as $program) {
            Program::create($program);
        }
    }
}

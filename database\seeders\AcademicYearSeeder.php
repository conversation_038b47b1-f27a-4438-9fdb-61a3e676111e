<?php

namespace Database\Seeders;

use App\Enums\AcademicSemesterEnum;
use App\Enums\AcademicYearStatusEnum;
use App\Models\AcademicYear;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AcademicYearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing academic years to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        AcademicYear::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $currentYear = date('Y');
        $academicYears = [
            [
                'name' => $currentYear.'/'.($currentYear + 1),
                'semester' => AcademicSemesterEnum::ODD->value,
                'start_date' => $currentYear.'-07-01',
                'end_date' => $currentYear.'-12-31',
                'status' => AcademicYearStatusEnum::ACTIVE->value,
            ],
            [
                'name' => $currentYear.'/'.($currentYear + 1),
                'semester' => AcademicSemesterEnum::EVEN->value,
                'start_date' => ($currentYear + 1).'-01-01',
                'end_date' => ($currentYear + 1).'-06-30',
                'status' => AcademicYearStatusEnum::PLANNED->value,
            ],
            [
                'name' => ($currentYear + 1).'/'.($currentYear + 2),
                'semester' => AcademicSemesterEnum::ODD->value,
                'start_date' => ($currentYear + 1).'-07-01',
                'end_date' => ($currentYear + 1).'-12-31',
                'status' => AcademicYearStatusEnum::PLANNED->value,
            ],
            [
                'name' => ($currentYear + 1).'/'.($currentYear + 2),
                'semester' => AcademicSemesterEnum::EVEN->value,
                'start_date' => ($currentYear + 2).'-01-01',
                'end_date' => ($currentYear + 2).'-06-30',
                'status' => AcademicYearStatusEnum::PLANNED->value,
            ],
        ];

        foreach ($academicYears as $academicYear) {
            AcademicYear::create($academicYear);
        }
    }
}

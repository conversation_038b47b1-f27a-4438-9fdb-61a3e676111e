<?php

namespace App\Exceptions;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class BusinessLogicException extends Exception
{
    /**
     * @var int
     */
    protected $statusCode = Response::HTTP_BAD_REQUEST;

    /**
     * @var array
     */
    protected $errors = [];

    /**
     * @var array
     */
    protected $headers = [];

    /**
     * BusinessLogicException constructor.
     */
    public function __construct(
        string $message = 'Terjadi kesalahan pada logika bisnis',
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        if ($statusCode !== null) {
            $this->statusCode = $statusCode;
        }

        $this->errors = $errors;
        $this->headers = $headers;
    }

    /**
     * Get the HTTP status code.
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the errors array.
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the headers array.
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }
}

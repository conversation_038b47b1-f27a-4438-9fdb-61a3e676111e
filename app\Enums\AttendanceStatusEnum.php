<?php

namespace App\Enums;

enum AttendanceStatusEnum: string
{
    case PRESENT = 'present';
    case ABSENT = 'absent';
    case LATE = 'late';

    /**
     * Get all attendance status options as an array
     */
    public static function options(): array
    {
        return [
            self::PRESENT->value => 'Hadir',
            self::ABSENT->value => 'Tidak Hadir',
            self::LATE->value => 'Terlambat',
        ];
    }

    /**
     * Get all status values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the display label for a status value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get color class for the status
     */
    public function color(): string
    {
        return match ($this) {
            self::PRESENT => 'success',
            self::ABSENT => 'danger',
            self::LATE => 'warning',
        };
    }
}

<?php

namespace Database\Seeders;

use App\Enums\ShiftStatusEnum;
use App\Models\Shift;
use Illuminate\Database\Seeder;

class ShiftSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shifts = [
            [
                'name' => 'Pagi',
                'description' => 'Shift pagi untuk kegiatan belajar mengajar',
                'status' => ShiftStatusEnum::ACTIVE,
            ],
            [
                'name' => 'Siang',
                'description' => 'Shift siang untuk kegiatan belajar mengajar',
                'status' => ShiftStatusEnum::ACTIVE,
            ],
            [
                'name' => 'Sore',
                'description' => 'Shift sore untuk kegiatan belajar mengajar',
                'status' => ShiftStatusEnum::ACTIVE,
            ],
            [
                'name' => 'Malam',
                'description' => 'Shift malam untuk kegiatan belajar mengajar',
                'status' => ShiftStatusEnum::INACTIVE,
            ],
        ];

        foreach ($shifts as $shift) {
            Shift::create($shift);
        }
    }
}

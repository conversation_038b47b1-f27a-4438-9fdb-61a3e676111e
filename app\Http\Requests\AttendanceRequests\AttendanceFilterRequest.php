<?php

namespace App\Http\Requests\AttendanceRequests;

use App\Enums\AttendanceStatusEnum;
use App\Enums\AttendantTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttendanceFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'class_schedule_id' => 'nullable|exists:class_schedules,id',
            'student_id' => 'nullable|exists:students,id',
            'teacher_id' => 'nullable|exists:teachers,id',
            'classroom_id' => 'nullable|exists:classrooms,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'attendance_status' => ['nullable', Rule::in(AttendanceStatusEnum::values())],
            'attendant_type' => ['nullable', Rule::in(AttendantTypeEnum::values())],
            'search' => 'nullable|string|max:100',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'class_schedule_id' => 'Jadwal Kelas',
            'student_id' => 'Siswa',
            'teacher_id' => 'Guru',
            'classroom_id' => 'Kelas',
            'date_from' => 'Tanggal Mulai',
            'date_to' => 'Tanggal Selesai',
            'attendance_status' => 'Status Kehadiran',
            'attendant_type' => 'Tipe Kehadiran',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'class_schedule_id.exists' => 'Jadwal kelas tidak valid',
            'student_id.exists' => 'Siswa tidak valid',
            'teacher_id.exists' => 'Guru tidak valid',
            'classroom_id.exists' => 'Kelas tidak valid',
            'date_from.date' => 'Format tanggal mulai tidak valid',
            'date_to.date' => 'Format tanggal selesai tidak valid',
            'date_to.after_or_equal' => 'Tanggal selesai harus setelah atau sama dengan tanggal mulai',
            'attendance_status.in' => 'Status kehadiran tidak valid',
            'attendant_type.in' => 'Tipe kehadiran tidak valid',
            'search.max' => 'Pencarian maksimal 100 karakter',
        ];
    }
}

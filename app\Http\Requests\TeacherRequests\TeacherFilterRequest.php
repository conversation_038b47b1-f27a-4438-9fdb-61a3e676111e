<?php

namespace App\Http\Requests\TeacherRequests;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;

class TeacherFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'gender' => ['sometimes', 'nullable', 'string'],
            'search' => ['sometimes', 'nullable', 'string'],
            'status' => ['sometimes', 'nullable', 'string'],
            'role' => ['sometimes', 'nullable', 'string'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes()
    {
        return [
            'gender' => 'Jenis kelamin',
            'search' => 'Pencarian',
            'status' => 'Status',
            'role' => 'Peran',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Handle empty string as null for proper filter application
        $this->merge([
            'gender' => $this->gender === '' ? null : $this->gender,
            'status' => $this->status === '' ? null : $this->status,
            'role' => $this->role === '' ? null : $this->role,
            'search' => $this->search === '' ? null : $this->search,
        ]);
    }
}

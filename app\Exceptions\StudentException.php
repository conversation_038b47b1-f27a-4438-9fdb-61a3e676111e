<?php

namespace App\Exceptions;

use Exception;

/**
 * Custom exception for student-related business logic errors
 */
class StudentException extends Exception
{
    /**
     * Create a new student exception instance.
     */
    public function __construct(string $message = 'Student operation failed', int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Exception for when a student cannot be deleted due to existing enrollments
     */
    public static function cannotDeleteWithEnrollments(): self
    {
        return new self('Siswa tidak dapat dihapus karena masih memiliki pendaftaran kelas aktif');
    }

    /**
     * Exception for when a student cannot change their own status
     */
    public static function cannotChangeOwnStatus(): self
    {
        return new self('Tidak dapat mengubah status akun sendiri');
    }

    /**
     * Exception for when a student email is already in use
     */
    public static function emailAlreadyExists(string $email): self
    {
        return new self("Email '{$email}' sudah digunakan oleh siswa lain");
    }

    /**
     * Exception for when a student username is already in use
     */
    public static function usernameAlreadyExists(string $username): self
    {
        return new self("Username '{$username}' sudah digunakan oleh siswa lain");
    }

    /**
     * Exception for when a student NIS is already in use
     */
    public static function nisAlreadyExists(string $nis): self
    {
        return new self("NIS '{$nis}' sudah digunakan oleh siswa lain");
    }

    /**
     * Exception for when a student NISN is already in use
     */
    public static function nisnAlreadyExists(string $nisn): self
    {
        return new self("NISN '{$nisn}' sudah digunakan oleh siswa lain");
    }

    /**
     * Exception for when a student is already enrolled in a classroom for the same academic year
     */
    public static function alreadyEnrolledInClassroom(): self
    {
        return new self('Siswa sudah terdaftar di kelas untuk tahun akademik yang sama');
    }

    /**
     * Exception for when trying to enroll a student in a full classroom
     */
    public static function classroomFull(string $classroomName): self
    {
        return new self("Kelas '{$classroomName}' sudah penuh, tidak dapat menambah siswa lagi");
    }

    /**
     * Exception for when a student enrollment cannot be deleted due to existing attendance records
     */
    public static function cannotDeleteEnrollmentWithAttendance(): self
    {
        return new self('Pendaftaran siswa tidak dapat dihapus karena sudah ada catatan kehadiran');
    }

    /**
     * Exception for when trying to enroll a student in an inactive academic year
     */
    public static function cannotEnrollInInactiveAcademicYear(): self
    {
        return new self('Tidak dapat mendaftarkan siswa pada tahun akademik yang tidak aktif');
    }

    /**
     * Exception for when a student's entry year is invalid
     */
    public static function invalidEntryYear(int $year): self
    {
        return new self("Tahun masuk '{$year}' tidak valid");
    }

    /**
     * Exception for when a student is too young or too old for enrollment
     */
    public static function invalidAge(int $age): self
    {
        return new self("Usia siswa ({$age} tahun) tidak memenuhi syarat untuk pendaftaran");
    }

    /**
     * Exception for when required parent information is missing
     */
    public static function missingParentInformation(): self
    {
        return new self('Informasi orang tua/wali siswa harus dilengkapi');
    }

    /**
     * Exception for when a student profile picture upload fails
     */
    public static function profilePictureUploadFailed(): self
    {
        return new self('Gagal mengunggah foto profil siswa');
    }

    /**
     * Student not enrolled in classroom error
     */
    public static function notEnrolledInClassroom(int $studentId, int $classroomId): self
    {
        return new self("Siswa dengan ID {$studentId} tidak terdaftar di kelas dengan ID {$classroomId}.");
    }
}

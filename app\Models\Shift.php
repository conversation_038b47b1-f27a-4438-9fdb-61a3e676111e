<?php

namespace App\Models;

use App\Enums\ShiftStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Shift extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => ShiftStatusEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        //
    ];

    /**
     * Get the lesson hours for the shift.
     */
    public function lessonHours(): HasMany
    {
        return $this->hasMany(LessonHour::class);
    }

    /**
     * Get the classrooms for the shift.
     */
    public function classrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    /**
     * Scope a query to only include active shifts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', ShiftStatusEnum::ACTIVE);
    }

    /**
     * Scope a query to only include inactive shifts.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', ShiftStatusEnum::INACTIVE);
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute()
    {
        return $this->status->label();
    }

    /**
     * Get the status color class.
     */
    public function getStatusColorAttribute()
    {
        return $this->status->color();
    }
}

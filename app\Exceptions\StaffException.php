<?php

namespace App\Exceptions;

use Exception;

/**
 * Custom exception for staff-related business logic errors
 */
class StaffException extends Exception
{
    /**
     * Create a new staff exception instance.
     */
    public function __construct(string $message = 'Staff operation failed', int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Exception for when a staff member cannot be deleted due to being the last admin
     */
    public static function cannotDeleteLastAdmin(): self
    {
        return new self('Tidak dapat menghapus administrator terakhir');
    }

    /**
     * Exception for when a staff member cannot change their own status
     */
    public static function cannotChangeOwnStatus(): self
    {
        return new self('Tidak dapat mengubah status akun sendiri');
    }

    /**
     * Exception for when a staff email is already in use
     */
    public static function emailAlreadyExists(string $email): self
    {
        return new self("Email '{$email}' sudah digunakan oleh staf lain");
    }

    /**
     * Exception for when a staff username is already in use
     */
    public static function usernameAlreadyExists(string $username): self
    {
        return new self("Username '{$username}' sudah digunakan oleh staf lain");
    }

    /**
     * Exception for when trying to assign an invalid role to staff
     */
    public static function invalidRole(string $role): self
    {
        return new self("Role '{$role}' tidak valid untuk staf");
    }

    /**
     * Exception for when maximum number of admins is reached
     */
    public static function maxAdminsReached(int $maxCount): self
    {
        return new self("Maksimal jumlah administrator ({$maxCount}) telah tercapai");
    }

    /**
     * Exception for when trying to deactivate the last active admin
     */
    public static function cannotDeactivateLastAdmin(): self
    {
        return new self('Tidak dapat menonaktifkan administrator terakhir yang aktif');
    }

    /**
     * Exception for when a staff member has insufficient permissions
     */
    public static function insufficientPermissions(): self
    {
        return new self('Staf tidak memiliki izin yang cukup untuk melakukan operasi ini');
    }

    /**
     * Exception for when trying to delete an active staff member
     */
    public static function cannotDeleteActiveStaff(): self
    {
        return new self('Tidak dapat menghapus staf yang masih aktif');
    }

    /**
     * Exception for when a principal already exists and trying to create another
     */
    public static function principalAlreadyExists(): self
    {
        return new self('Kepala sekolah sudah ada, hanya boleh ada satu kepala sekolah');
    }

    /**
     * Exception for when a treasurer already exists and trying to create another
     */
    public static function treasurerAlreadyExists(): self
    {
        return new self('Bendahara sudah ada, hanya boleh ada satu bendahara');
    }

    /**
     * Exception for when trying to change role of a staff with active responsibilities
     */
    public static function cannotChangeRoleWithActiveResponsibilities(): self
    {
        return new self('Tidak dapat mengubah role staf yang masih memiliki tanggung jawab aktif');
    }

    /**
     * Exception for when staff member is required for system operation
     */
    public static function requiredForSystemOperation(): self
    {
        return new self('Staf ini diperlukan untuk operasi sistem dan tidak dapat dihapus');
    }

    /**
     * Exception for when trying to perform operation on non-staff user
     */
    public static function notAStaffMember(): self
    {
        return new self('Pengguna bukan merupakan anggota staf');
    }

    /**
     * Exception for when staff data validation fails
     */
    public static function validationFailed(string $field): self
    {
        return new self("Validasi data staf gagal pada field: {$field}");
    }
}

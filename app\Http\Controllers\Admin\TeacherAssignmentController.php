<?php

namespace App\Http\Controllers\Admin;

use App\Models\Subject;
use App\Models\Teacher;
use App\Models\Classroom;
use App\Models\AcademicYear;
use App\Models\TeacherAssignment;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentStoreRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentDeleteRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentFilterRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentUpdateRequest;

class TeacherAssignmentController extends Controller
{
    /**
     * Display a listing of teacher assignments.
     */
    public function index(TeacherAssignmentFilterRequest $request): View|JsonResponse
    {
        $query = TeacherAssignment::with(['teacher.user', 'subject.program', 'classroom', 'academicYear']);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatTeacherAssignmentForDatatable($query);
        }

        return view('admin.pages.teacher-assignment.index', [
            'teachers' => Teacher::whereHas('user', function ($q) {
                $q->where('status', 'active');
            })->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::where('status', 'active')->get(),
            'academicYears' => AcademicYear::where('status', 'active')->get(),
            'initialFilters' => $request->validated(),
        ]);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by classroom
        if (!empty($filters['classroom_id'])) {
            $query->where('classroom_id', $filters['classroom_id']);
        }

        // Filter by academic year
        if (!empty($filters['academic_year_id'])) {
            $query->where('academic_year_id', $filters['academic_year_id']);
        }

        // Filter by teacher
        if (!empty($filters['teacher_id'])) {
            $query->where('teacher_id', $filters['teacher_id']);
        }

        // Filter by subject
        if (!empty($filters['subject_id'])) {
            $query->where('subject_id', $filters['subject_id']);
        }

        // Filter by homeroom teacher status
        if (isset($filters['is_homeroom_teacher'])) {
            $query->where('is_homeroom_teacher', $filters['is_homeroom_teacher']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('teacher.user', function ($userQuery) use ($searchTerm) {
                    $userQuery->where('name', 'like', $searchTerm);
                })
                    ->orWhereHas('subject', function ($subjectQuery) use ($searchTerm) {
                        $subjectQuery->where('name', 'like', $searchTerm);
                    })
                    ->orWhereHas('classroom', function ($classroomQuery) use ($searchTerm) {
                        $classroomQuery->where('name', 'like', $searchTerm);
                    });
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables.
     */
    protected function formatTeacherAssignmentForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('teacher_name', function ($row) {
                return $row->teacher?->user?->name ?? '-';
            })
            ->editColumn('teacher_email', function ($row) {
                return $row->teacher?->user?->email ?? '-';
            })
            ->editColumn('subject_name', function ($row) {
                return $row->subject?->name ?? '-';
            })
            ->editColumn('classroom_name', function ($row) {
                return $row->classroom?->name ?? '-';
            })
            ->editColumn('academic_year_name', function ($row) {
                return $row->academicYear?->name ?? '-';
            })
            ->editColumn('program_name', function ($row) {
                return $row->subject?->program?->name ?? '-';
            })
            ->editColumn('assignment_type', function ($row) {
                return $row->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel';
            })
            ->editColumn('status', function ($row) {
                $badgeClass = $row->is_homeroom_teacher ? 'success' : 'primary';
                $text = $row->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel';
                return '<span class="badge bg-' . $badgeClass . ' text-uppercase">' . $text . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.teacher-assignment._action', [
                    'edit' => route('admin.teacher-assignments.edit', $row->id),
                    'destroy' => route('admin.teacher-assignments.destroy', $row->id),
                    'id' => $row->id,
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new teacher assignment.
     */
    public function create(): View
    {
        return view('admin.pages.teacher-assignment.create', [
            'teachers' => Teacher::whereHas('user', function ($q) {
                $q->where('status', 'active');
            })->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::where('status', 'active')->get(),
            'academicYears' => AcademicYear::where('status', 'active')->get(),
        ]);
    }

    /**
     * Store a newly created teacher assignment.
     */
    public function store(TeacherAssignmentStoreRequest $request): JsonResponse
    {
        try {
            $teacherAssignment = TeacherAssignment::create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dibuat.',
                'data' => $teacherAssignment,
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            \Log::error('Error creating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified teacher assignment.
     */
    public function edit(int $id): View
    {
        $teacherAssignment = TeacherAssignment::with(['teacher.user', 'subject', 'classroom', 'academicYear'])
            ->findOrFail($id);

        return view('admin.pages.teacher-assignment.edit', [
            'teacherAssignment' => $teacherAssignment,
            'teachers' => Teacher::whereHas('user', function ($q) {
                $q->where('status', 'active');
            })->get(),
            'subjects' => Subject::all(),
            'classrooms' => Classroom::where('status', 'active')->get(),
            'academicYears' => AcademicYear::where('status', 'active')->get(),
        ]);
    }

    /**
     * Update the specified teacher assignment.
     */
    public function update(TeacherAssignmentUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $teacherAssignment = TeacherAssignment::findOrFail($id);
            $teacherAssignment->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil diperbarui.',
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified teacher assignment.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $teacherAssignment = TeacherAssignment::findOrFail($id);

            // Check if there are related class schedules
            if ($teacherAssignment->classSchedules()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus penugasan guru yang memiliki jadwal kelas.',
                ], Response::HTTP_FORBIDDEN);
            }

            $teacherAssignment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dihapus.',
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}



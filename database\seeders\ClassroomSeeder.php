<?php

namespace Database\Seeders;

use App\Enums\ClassroomLevelEnum;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Program;
use App\Models\Teacher;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClassroomSeeder extends Seeder
{
    /**
     * Jumlah kelas per tingkat (contoh: VII A, VII B, VII C)
     */
    private const CLASSROOM_PER_LEVEL = 3;

    /**
     * Tingkat kelas yang digunakan untuk seeding
     */
    private const CLASSROOM_LEVELS = [
        ClassroomLevelEnum::SEVEN,
        ClassroomLevelEnum::EIGHT,
        ClassroomLevelEnum::NINE,
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $programs = Program::all();
        $academicYear = AcademicYear::where('status', 'active')->first();
        $teachers = Teacher::all();

        if (!$academicYear) {
            $this->command->error('❌ Tidak ditemukan Tahun Akademik yang aktif. Silakan jalankan seeder AcademicYear terlebih dahulu.');
            return;
        }

        if ($teachers->isEmpty()) {
            $this->command->error('❌ Data guru kosong. Silakan jalankan seeder Teacher terlebih dahulu.');
            return;
        }

        if ($programs->isEmpty()) {
            $this->command->error('❌ Tidak ditemukan data program. Silakan jalankan seeder Program terlebih dahulu.');
            return;
        }

        // Kosongkan data kelas sebelum mengisi ulang
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Classroom::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        foreach ($programs as $program) {
            foreach (self::CLASSROOM_LEVELS as $levelEnum) {
                for ($i = 1; $i <= self::CLASSROOM_PER_LEVEL; $i++) {
                    $suffix = chr(64 + $i); // A, B, C
                    $level = $levelEnum->value;
                    $label = $levelEnum->label(); // pakai method langsung
                    $teacher = $teachers->random();

                    Classroom::create([
                        'name' => "$level $suffix",
                        'level' => $level,
                        'capacity' => 32,
                        'program_id' => $program->id,
                        'academic_year_id' => $academicYear->id,
                        'teacher_id' => $teacher->id,
                        'status' => 'active',
                        'description' => "{$program->name} - {$label} Kelas $suffix",
                    ]);
                }
            }
        }

        $this->command->info('✅ ClassroomSeeder berhasil dijalankan.');
    }
}

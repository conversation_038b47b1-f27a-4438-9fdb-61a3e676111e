<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'teacher_id' => 'required|integer|exists:teachers,id',
            'subject_id' => 'nullable|integer|exists:subjects,id',
            'classroom_id' => 'required|integer|exists:classrooms,id',
            'academic_year_id' => 'required|integer|exists:academic_years,id',
            'is_homeroom_teacher' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'teacher_id.required' => 'Guru wajib dipilih',
            'teacher_id.exists' => 'Guru yang dipilih tidak valid',
            'subject_id.exists' => 'Mata pelajaran yang dipilih tidak valid',
            'classroom_id.required' => 'Kelas wajib dipilih',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid',
            'academic_year_id.required' => 'Tahun akademik wajib dipilih',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid',
            'is_homeroom_teacher.boolean' => 'Status wali kelas harus berupa nilai boolean',
        ];
    }
}

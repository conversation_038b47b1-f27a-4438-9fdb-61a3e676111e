<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\LessonHourRequests\LessonHourFilterRequest;
use App\Http\Requests\LessonHourRequests\LessonHourStoreRequest;
use App\Http\Requests\LessonHourRequests\LessonHourUpdateRequest;
use App\Services\ClassroomService;
use App\Services\LessonHourService;
use App\Services\ShiftService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class LessonHourController extends Controller
{
    // ...existing code...

    /**
     * Display a listing of lesson hours
     *
     * @return View|JsonResponse
     */
    public function index(LessonHourFilterRequest $request)
    {
        $query = \App\Models\LessonHour::query();
        $filters = $request->validated();
        if (isset($filters['classroom_id'])) {
            $query->where('classroom_id', $filters['classroom_id']);
        }
        if (isset($filters['shift_id'])) {
            $query->where('shift_id', $filters['shift_id']);
        }
        $lessonHours = $query->with(['classroom', 'shift'])->orderBy('sequence')->get();

        if ($request->ajax()) {
            return $this->datatableResponse($lessonHours);
        }

        $classrooms = \App\Models\Classroom::where('status', 'active')->get();
        $shifts = \App\Models\Shift::where('status', 'active')->get();

        return view('admin.pages.lesson-hour.index', [
            'lessonHours' => $lessonHours,
            'classrooms' => $classrooms,
            'shifts' => $shifts,
        ]);
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        $result = [];
        $counter = 0;

        foreach ($data as $item) {
            $counter++;
            $result[] = [
                'DT_RowIndex' => $counter,
                'id' => $item->id,
                'name' => $item->name,
                'time' => $item->formatted_start_time . ' - ' . $item->formatted_end_time,
                'sequence' => $item->sequence,
                'classroom' => $item->classroom ? $item->classroom->name : 'Global',
                'shift' => $item->shift ? $item->shift->name : 'Global',
                'action' => view('admin.pages.lesson-hour.action', compact('item'))->render(),
            ];
        }

        return response()->json([
            'draw' => request()->input('draw'),
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $result,
        ]);
    }

    /**
     * Show the form for creating a new lesson hour
     */
    public function create(): View
    {
        $classrooms = \App\Models\Classroom::where('status', 'active')->get();
        $shifts = \App\Models\Shift::where('status', 'active')->get();
        return view('admin.pages.lesson-hour.create', [
            'classrooms' => $classrooms,
            'shifts' => $shifts,
        ]);
    }

    /**
     * Store a newly created lesson hour
     */
    public function store(LessonHourStoreRequest $request): JsonResponse
    {
        try {
            $lessonHour = \App\Models\LessonHour::create($request->validated());
            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil dibuat',
                'data' => $lessonHour,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Show the form for editing the specified lesson hour
     */
    public function edit(int $id): View
    {
        $lessonHour = \App\Models\LessonHour::with(['classroom', 'shift'])->findOrFail($id);
        $classrooms = \App\Models\Classroom::where('status', 'active')->get();
        $shifts = \App\Models\Shift::where('status', 'active')->get();
        return view('admin.pages.lesson-hour.edit', [
            'lessonHour' => $lessonHour,
            'classrooms' => $classrooms,
            'shifts' => $shifts,
        ]);
    }

    /**
     * Update the specified lesson hour
     */
    public function update(LessonHourUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $lessonHour = \App\Models\LessonHour::findOrFail($id);
            $updated = $lessonHour->update($request->validated());
            if (!$updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tidak ada perubahan data',
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil diperbarui',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Remove the specified lesson hour
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $lessonHour = \App\Models\LessonHour::findOrFail($id);
            $lessonHour->delete();
            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil dihapus',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}

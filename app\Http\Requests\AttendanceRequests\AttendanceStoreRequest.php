<?php

namespace App\Http\Requests\AttendanceRequests;

use App\Enums\AttendanceStatusEnum;
use App\Enums\AttendantTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttendanceStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'class_schedule_id' => 'required|exists:class_schedules,id',
            'attendance_date' => 'required|date',
            'attendance_status' => ['required', Rule::in(AttendanceStatusEnum::values())],
            'attendant_type' => ['required', Rule::in(AttendantTypeEnum::values())],
            'notes' => 'nullable|string|max:500',
            'longitude' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
        ];

        // Add conditional validation based on attendant_type
        if ($this->input('attendant_type') === AttendantTypeEnum::STUDENT->value) {
            $rules['student_id'] = 'required|exists:students,id';
        } elseif ($this->input('attendant_type') === AttendantTypeEnum::TEACHER->value) {
            $rules['teacher_id'] = 'required|exists:teachers,id';
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'class_schedule_id' => 'Jadwal Kelas',
            'attendance_date' => 'Tanggal Kehadiran',
            'attendance_status' => 'Status Kehadiran',
            'attendant_type' => 'Tipe Kehadiran',
            'student_id' => 'Siswa',
            'teacher_id' => 'Guru',
            'notes' => 'Catatan',
            'longitude' => 'Longitude',
            'latitude' => 'Latitude',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'class_schedule_id.required' => 'Jadwal kelas harus dipilih',
            'class_schedule_id.exists' => 'Jadwal kelas tidak valid',
            'attendance_date.required' => 'Tanggal kehadiran harus diisi',
            'attendance_date.date' => 'Format tanggal kehadiran tidak valid',
            'attendance_status.required' => 'Status kehadiran harus dipilih',
            'attendance_status.in' => 'Status kehadiran tidak valid',
            'attendant_type.required' => 'Tipe kehadiran harus dipilih',
            'attendant_type.in' => 'Tipe kehadiran tidak valid',
            'student_id.required' => 'Siswa harus dipilih',
            'student_id.exists' => 'Siswa tidak valid',
            'teacher_id.required' => 'Guru harus dipilih',
            'teacher_id.exists' => 'Guru tidak valid',
            'notes.max' => 'Catatan maksimal 500 karakter',
            'longitude.numeric' => 'Longitude harus berupa angka',
            'latitude.numeric' => 'Latitude harus berupa angka',
        ];
    }
}

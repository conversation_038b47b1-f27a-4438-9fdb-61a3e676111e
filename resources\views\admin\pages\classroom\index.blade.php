@extends('admin.layouts.app')

@section('title', 'Kelas')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Daftar Kelas',
        'breadcrumb' => 'Manajemen Sekolah',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar Kelas
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-classrooms">0</span>
                            </h5>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <a href="{{ route('admin.classrooms.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Program Dropdown -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-program" class="form-label">Program</label>
                                <select class="form-select" data-choices name="program_id" id="filter-program">
                                    <option value="">Semua Program</option>
                                    @foreach ($programs as $program)
                                        <option value="{{ $program->id }}" {{ isset($initialFilters['program_id']) && $initialFilters['program_id'] == $program->id ? 'selected' : '' }}>
                                            {{ $program->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Level Dropdown -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-level" class="form-label">Level</label>
                                <select class="form-select" data-choices name="level" id="filter-level">
                                    <option value="">Semua Level</option>
                                    @foreach ($levels as $value => $label)
                                        <option value="{{ $value }}" {{ isset($initialFilters['level']) && $initialFilters['level'] == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Status Dropdown -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}" {{ isset($initialFilters['status']) && $initialFilters['status'] == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama kelas, program..." id="search-input" name="search" value="{{ $initialFilters['search'] ?? '' }}">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="classroom-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama Kelas</th>
                                    <th>Level</th>
                                    <th>Program</th>
                                    <th>Shift</th>
                                    <th>Wali Kelas</th>
                                    <th>Tahun Akademik</th>
                                    <th>Kapasitas</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Inisialisasi DataTable
            const classroomTable = $('#classroom-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.classrooms.index') }}",
                    data: d => {
                        d.program_id = $('#filter-program').val();
                        d.level = $('#filter-level').val();
                        d.status = $('#filter-status').val();
                        d.search = $('#search-input').val();
                    },
                    complete: response => {
                        const totalClassrooms = response.responseJSON?.recordsTotal || 0;
                        $('#total-classrooms').text(totalClassrooms);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data kelas.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'name',
                    name: 'name',
                }, {
                    data: 'level',
                    name: 'level',
                }, {
                    data: 'program_name',
                    name: 'program.name',
                }, {
                    data: 'shift_name',
                    name: 'shift.name',
                }, {
                    data: 'teacher_name',
                    name: 'teacher.user.name',
                }, {
                    data: 'academic_year',
                    name: 'academicYear.name',
                }, {
                    data: 'capacity_status',
                    name: 'capacity',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'status',
                    name: 'status',
                }, {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                }],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: 'Cari...',
                    lengthMenu: 'Tampilkan _MENU_ data',
                    zeroRecords: 'Data tidak ditemukan',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                    infoFiltered: '(disaring dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya',
                    }
                },
                order: [
                    [1, 'asc']
                ] // Sort by name by default
            });

            // Fungsi untuk memuat ulang tabel
            const reloadTable = () => classroomTable.draw();

            // Fungsi untuk menampilkan pesan "Coming Soon"
            const showComingSoon = () => {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur ini akan segera tersedia.',
                    icon: 'info',
                });
            };

            // Fungsi untuk menangani penghapusan data
            const handleDelete = url => {
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data kelas ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true,
                }).then(result => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: response => {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500,
                                });
                                reloadTable();
                            },
                            error: xhr => {
                                const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            };

            // Event Listeners
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    classroomTable.columns.adjust().draw();
                }, 300);
            });

            $('#filter-program, #filter-level, #filter-status').change(reloadTable);
            $('#search-button').click(reloadTable);
            $('#search-input').keyup(e => e.key === 'Enter' && reloadTable());

            $('#export-btn').click(showComingSoon);

            $('#classroom-table').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan.',
                        icon: 'error',
                    });
                    return;
                }
                handleDelete(url);
            });
        });
    </script>
@endpush

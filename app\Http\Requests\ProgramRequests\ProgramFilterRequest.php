<?php

namespace App\Http\Requests\ProgramRequests;

use Illuminate\Foundation\Http\FormRequest;

class ProgramFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'sometimes|nullable|string|in:active,inactive',
            'search' => 'sometimes|nullable|string|max:255',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status' => 'Status',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Handle empty string as null for proper filter application
        $this->merge([
            'status' => $this->status === '' ? null : $this->status,
            'search' => $this->search === '' ? null : $this->search,
        ]);
    }
}

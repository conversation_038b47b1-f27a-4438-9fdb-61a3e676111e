<?php

namespace App\Enums;

enum AcademicYearStatusEnum: string
{
    case PLANNED = 'planned';
    case ACTIVE = 'active';
    case COMPLETED = 'completed';

    /**
     * Get label for the enum instance
     */
    public function label(): string
    {
        return match ($this) {
            self::PLANNED => 'Direncanakan',
            self::ACTIVE => 'Aktif',
            self::COMPLETED => 'Selesai',
        };
    }

    /**
     * Get all status options as array [label] (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::PLANNED->value => self::PLANNED->label(),
            self::ACTIVE->value => self::ACTIVE->label(),
            self::COMPLETED->value => self::COMPLETED->label(),
        ];
    }

    /**
     * Static access to label by value (string or enum)
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            return $value->label();
        }

        return self::tryFrom($value)?->label() ?? $value;
    }

    public function color(): string
    {
        return match ($this) {
            self::PLANNED => 'warning',
            self::ACTIVE => 'success',
            self::COMPLETED => 'info',
        };
    }
    public static function values(): array
    {
        return array_map(fn(self $case) => $case->value, self::cases());
    }
}

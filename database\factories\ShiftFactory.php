<?php

namespace Database\Factories;

use App\Enums\ShiftStatusEnum;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shift>
 */
class ShiftFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Shift::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement(['Pagi', 'Siang', 'Sore', 'Malam']),
            'description' => $this->faker->optional()->sentence(),
            'status' => $this->faker->randomElement(ShiftStatusEnum::values()),
        ];
    }

    /**
     * Indicate that the shift is active.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => ShiftStatusEnum::ACTIVE,
        ]);
    }

    /**
     * Indicate that the shift is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => ShiftStatusEnum::INACTIVE,
        ]);
    }
}

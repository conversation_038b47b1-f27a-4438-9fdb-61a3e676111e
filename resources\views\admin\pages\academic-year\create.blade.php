@extends('admin.layouts.app')

@section('title', 'Tambah Tahun Akademik')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Tambah Tahun Akademik',
        'breadcrumb' => 'Manajemen Akademik',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            Tambah Tahun Akademik Baru
                        </h5>
                        <div class="flex-shrink-0">
                            <a href="{{ route('admin.academic-years.index') }}" class="btn btn-soft-danger">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form id="create-form" method="POST" action="{{ route('admin.academic-years.store') }}">
                        @csrf

                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-lg-6">
                                <div class="card shadow-none border">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Informasi Dasar</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <!-- Name -->
                                            <div class="col-12">
                                                <label for="name" class="form-label">Nama Tahun Akademik <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="name" name="name"
                                                       value="{{ old('name') }}" placeholder="contoh: 2023/2024" required>
                                                <div class="invalid-feedback" data-field="name"></div>
                                            </div>

                                            <!-- Semester -->
                                            <div class="col-12">
                                                <label for="semester" class="form-label">Semester <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="semester" name="semester" required>
                                                    <option value="" selected disabled>Pilih Semester</option>
                                                    @foreach ($semesters as $value => $label)
                                                        <option value="{{ $value }}" {{ old('semester') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                                <div class="invalid-feedback" data-field="semester"></div>
                                            </div>

                                            <!-- Description -->
                                            <div class="col-12">
                                                <label for="description" class="form-label">Deskripsi (Opsional)</label>
                                                <textarea class="form-control" id="description" name="description"
                                                          rows="3" placeholder="Tambahkan keterangan tambahan jika ada" style="height: 56px;">{{ old('description') }}</textarea>
                                                <div class="invalid-feedback" data-field="description"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date & Status Information -->
                            <div class="col-lg-6">
                                <div class="card shadow-none border">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Periode & Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <!-- Start Date -->
                                            <div class="col-12">
                                                <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control" id="start_date" name="start_date"
                                                       value="{{ old('start_date') }}" required>
                                                <div class="invalid-feedback" data-field="start_date"></div>
                                            </div>

                                            <!-- End Date -->
                                            <div class="col-12">
                                                <label for="end_date" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control" id="end_date" name="end_date"
                                                       value="{{ old('end_date') }}" required>
                                                <div class="invalid-feedback" data-field="end_date"></div>
                                            </div>

                                            <!-- Status -->
                                            <div class="col-12">
                                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="status" name="status" required>
                                                    <option value="" selected disabled>Pilih Status</option>
                                                    @foreach ($statuses as $value => $label)
                                                        <option value="{{ $value }}" {{ old('status') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                                <div class="invalid-feedback" data-field="status"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.academic-years.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Real-time date validation
            $('#start_date, #end_date').on('change', function() {
                validateDates();
            });

            // Form submission
            $('#create-form').submit(function(e) {
                e.preventDefault();

                // Clear previous errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').empty();

                // Validate dates before submission
                if (!validateDates()) {
                    Swal.fire({
                        title: 'Perhatian!',
                        text: 'Harap perbaiki tanggal sebelum mengirim.',
                        icon: 'warning'
                    });
                    return;
                }

                // Set loading state
                const $submitBtn = $('#submit-btn');
                const originalText = $submitBtn.html();
                $submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...').prop('disabled', true);

                // Prepare form data
                const formData = new FormData(this);

                // AJAX request
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "{{ route('admin.academic-years.index') }}";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessages = [];
                            $.each(errors, function(field, messages) {
                                $(`[name="${field}"]`).addClass('is-invalid');
                                $(`[data-field="${field}"]`).html(messages.join('<br>'));
                                errorMessages.push(...messages);
                            });
                            Swal.fire({
                                title: 'Error!',
                                html: 'Terdapat kesalahan dalam formulir:<br>' + errorMessages.join('<br>'),
                                icon: 'error'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data',
                                icon: 'error'
                            });
                        }
                    },
                    complete: function() {
                        $submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Date validation function
            function validateDates() {
                const startDate = new Date($('#start_date').val());
                const endDate = new Date($('#end_date').val());
                const $endDateField = $('#end_date');
                const $endDateFeedback = $('[data-field="end_date"]');

                if (startDate && endDate && startDate > endDate) {
                    $endDateField.addClass('is-invalid');
                    $endDateFeedback.text('Tanggal selesai harus setelah tanggal mulai.');
                    return false;
                } else {
                    $endDateField.removeClass('is-invalid');
                    $endDateFeedback.empty();
                    return true;
                }
            }
        });
    </script>
@endpush

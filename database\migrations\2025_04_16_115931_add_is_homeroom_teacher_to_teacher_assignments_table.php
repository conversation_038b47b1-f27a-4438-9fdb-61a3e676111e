<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            // Add is_homeroom_teacher column
            $table->boolean('is_homeroom_teacher')->default(false)->after('academic_year_id');

            // Remove status column
            $table->dropColumn('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            // Revert changes by dropping is_homeroom_teacher column
            $table->dropColumn('is_homeroom_teacher');

            // Add back status column
            $table->boolean('status')->default(true)->after('academic_year_id');
        });
    }
};

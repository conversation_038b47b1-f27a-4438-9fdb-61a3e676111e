<?php

namespace App\Http\Requests\ShiftRequests;

use App\Enums\ShiftStatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ShiftStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:shifts,name',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:' . implode(',', ShiftStatusEnum::values()),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'Nama Shift',
            'description' => 'Deskripsi',
            'status' => 'Status',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Nama shift wajib diisi.',
            'name.unique' => 'Nama shift sudah digunakan.',
            'name.max' => 'Nama shift maksimal 255 karakter.',
            'description.max' => 'Deskripsi maksimal 1000 karakter.',
            'status.required' => 'Status wajib dipilih.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }
}

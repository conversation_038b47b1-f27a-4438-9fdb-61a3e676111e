<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\TeacherAssignment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeacherAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get active academic year
        $academicYear = AcademicYear::where('status', 'active')->first();
        if (! $academicYear) {
            $this->command->error('No active academic year found. Please run AcademicYearSeeder first.');

            return;
        }

        // Get all teachers, subjects, and classrooms
        $teachers = Teacher::all();
        $subjects = Subject::all();
        $classrooms = Classroom::all();

        if ($teachers->isEmpty() || $subjects->isEmpty() || $classrooms->isEmpty()) {
            $this->command->error('Teachers, subjects, or classrooms not found. Please run their seeders first.');

            return;
        }

        // Clear existing assignments to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('teacher_assignments')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Assign homeroom teachers to classrooms
        foreach ($classrooms as $classroom) {
            // Get a random teacher for homeroom
            $homeroomTeacher = $teachers->random();

            // Get a default subject for the homeroom teacher (first subject in the program)
            $defaultSubject = $subjects->where('program_id', $classroom->program_id)->first();

            if (! $defaultSubject) {
                $this->command->error('No subjects found for program ID: '.$classroom->program_id);

                continue;
            }

            TeacherAssignment::create([
                'teacher_id' => $homeroomTeacher->id,
                'subject_id' => $defaultSubject->id, // Assign a default subject to homeroom teacher
                'classroom_id' => $classroom->id,
                'academic_year_id' => $academicYear->id,
                'is_homeroom_teacher' => true,
            ]);

            // Assign subjects to teachers for this classroom
            // Each classroom gets 5-8 subjects
            $programSubjects = $subjects->where('program_id', $classroom->program_id);

            // Skip the subject already assigned to the homeroom teacher
            $remainingSubjects = $programSubjects->where('id', '!=', $defaultSubject->id);

            // Determine how many subjects to assign (5-8 or all available if fewer)
            $subjectCount = min(rand(5, 8), $remainingSubjects->count());

            // Get random subjects
            $classroomSubjects = $remainingSubjects->random($subjectCount);

            // Keep track of assigned subjects to avoid duplicates
            $assignedSubjects = collect([$defaultSubject->id]);

            foreach ($classroomSubjects as $subject) {
                // Skip if we've already assigned this subject
                if ($assignedSubjects->contains($subject->id)) {
                    continue;
                }

                // Get a random teacher (different from homeroom if possible)
                $availableTeachers = $teachers->where('id', '!=', $homeroomTeacher->id);
                $teacher = $availableTeachers->isEmpty() ? $homeroomTeacher : $availableTeachers->random();

                TeacherAssignment::create([
                    'teacher_id' => $teacher->id,
                    'subject_id' => $subject->id,
                    'classroom_id' => $classroom->id,
                    'academic_year_id' => $academicYear->id,
                    'is_homeroom_teacher' => false,
                ]);

                // Add to assigned subjects
                $assignedSubjects->push($subject->id);
            }
        }

        $this->command->info('Teacher assignments created successfully!');
    }
}

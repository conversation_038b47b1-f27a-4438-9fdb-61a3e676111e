<?php

namespace App\Enums;

enum AttendantTypeEnum: string
{
    case TEACHER = 'teacher';
    case STUDENT = 'student';

    /**
     * Get all attendant type options as an array
     */
    public static function options(): array
    {
        return [
            self::TEACHER->value => 'Guru',
            self::STUDENT->value => 'Siswa',
        ];
    }

    /**
     * Get all type values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the display label for a type value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }
}

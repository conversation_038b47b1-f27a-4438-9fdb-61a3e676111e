<?php

namespace App\Helpers;

use App\Exceptions\ApiException;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\Log;
use Throwable;

class ErrorHandler
{
    /**
     * Handle a business logic exception.
     *
     *
     * @throws BusinessLogicException
     */
    public static function businessLogic(
        string $message,
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        bool $shouldLog = true
    ): void {
        if ($shouldLog) {
            Log::warning($message, $errors);
        }

        throw new BusinessLogicException($message, $statusCode, $errors, $headers);
    }

    /**
     * Handle a database exception.
     *
     *
     * @throws DatabaseException
     */
    public static function database(
        string $message,
        ?Throwable $previous = null,
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        bool $shouldLog = true
    ): void {
        if ($shouldLog) {
            Log::error($message, [
                'exception' => $previous ? [
                    'message' => $previous->getMessage(),
                    'file' => $previous->getFile(),
                    'line' => $previous->getLine(),
                ] : null,
                'errors' => $errors,
            ]);
        }

        throw new DatabaseException(
            $message,
            $statusCode,
            $errors,
            $headers,
            0,
            $previous
        );
    }

    /**
     * Handle a not found exception.
     *
     *
     * @throws NotFoundException
     */
    public static function notFound(
        string $message = 'Data tidak ditemukan',
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        bool $shouldLog = true
    ): void {
        if ($shouldLog) {
            Log::notice($message, $errors);
        }

        throw new NotFoundException($message, $statusCode, $errors, $headers);
    }

    /**
     * Handle an API exception.
     *
     *
     * @throws ApiException
     */
    public static function api(
        string $message,
        ?int $statusCode = null,
        array $errors = [],
        array $headers = [],
        bool $shouldLog = true
    ): void {
        if ($shouldLog) {
            Log::warning($message, $errors);
        }

        throw new ApiException($message, $statusCode, $errors, $headers);
    }
}

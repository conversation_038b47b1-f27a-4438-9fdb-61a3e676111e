<?php

namespace App\Enums;

use ValueError;

enum RoleEnum: string
{
    case ADMIN = 'admin';
    case PRINCIPAL = 'principal';
    case TREASURER = 'treasurer';
    case SUBJECT_TEACHER = 'subject_teacher';
    case SUBSTITUTE_TEACHER = 'substitute_teacher';
    case STUDENT = 'student';

    /**
     * Get human-readable role names for display.
     */
    public function label(): string
    {
        return match ($this) {
            self::ADMIN => 'Administrator',
            self::PRINCIPAL => 'Kepala Sekolah',
            self::TREASURER => '<PERSON>ahara',
            self::SUBJECT_TEACHER => 'Guru Mapel',
            self::SUBSTITUTE_TEACHER => '<PERSON> Piket',
            self::STUDENT => 'Siswa',
        };
    }

    /**
     * Get all role values as an array (for database usage).
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all roles as key-value pairs (for dropdowns).
     */
    public static function dropdown(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn($case) => $case->label(), self::cases())
        );
    }

    /**
     * Get the label for a given role value.
     */
    public static function getLabel(?string $role): string
    {
        if ($role === null) {
            return 'Tidak Diketahui';
        }

        try {
            return self::from($role)->label();
        } catch (ValueError) {
            return $role;
        }
    }

    /**
     * Get staff role values (admin, principal, treasurer).
     */
    public static function staff(): array
    {
        return [
            self::ADMIN->value,
            self::PRINCIPAL->value,
            self::TREASURER->value,
        ];
    }

    /**
     * Get teacher role values (subject_teacher, substitute_teacher).
     */
    public static function teacher(): array
    {
        return [
            self::SUBJECT_TEACHER->value,
            self::SUBSTITUTE_TEACHER->value,
        ];
    }

    //staff options
    public static function staffOptions(): array
    {
        return [
            self::ADMIN->value => self::ADMIN->label(),
            self::PRINCIPAL->value => self::PRINCIPAL->label(),
            self::TREASURER->value => self::TREASURER->label(),
        ];
    }

    public static function teacherOptions(): array
    {
        return [
            self::SUBJECT_TEACHER->value => self::SUBJECT_TEACHER->label(),
            self::SUBSTITUTE_TEACHER->value => self::SUBSTITUTE_TEACHER->label(),
        ];
    }
}

@extends('admin.layouts.app')

@section('title', 'Edit Data Shift')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Data Shift',
        'breadcrumb' => 'Manajemen Kelas',
    ])

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Data Shift</h5>
                </div>
                <div class="card-body">
                    <form id="edit-shift-form" action="{{ route('admin.shifts.update', $shift->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Nama Shift <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ $shift->name }}" required>
                                <div class="invalid-feedback" data-field="name"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="status" name="status" required>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}" {{ $shift->status === $value ? 'selected' : '' }}>{{ $label }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="status"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Masukkan deskripsi shift (opsional)">{{ $shift->description }}</textarea>
                            <div class="invalid-feedback" data-field="description"></div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="ri-save-line align-bottom me-1"></i> Simpan Perubahan
                            </button>
                            <a href="{{ route('admin.shifts.index') }}" class="btn btn-light">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Shift</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-light text-primary rounded-3 fs-18">
                                    <i class="ri-calendar-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="fs-14 mb-1">Dibuat Pada</h6>
                            <p class="text-muted mb-0 fs-12">{{ $shift->created_at->format('d M Y H:i') }}</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-light text-info rounded-3 fs-18">
                                    <i class="ri-time-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="fs-14 mb-1">Terakhir Diperbarui</h6>
                            <p class="text-muted mb-0 fs-12">{{ $shift->updated_at->format('d M Y H:i') }}</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-light text-warning rounded-3 fs-18">
                                    <i class="ri-alert-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="fs-14 mb-1">Perhatian</h6>
                            <p class="text-muted mb-0 fs-12">Field dengan tanda <span class="text-danger">*</span> wajib diisi.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Form submission
            $('#edit-shift-form').on('submit', function(e) {
                e.preventDefault();

                var form = $(this);
                var submitBtn = $('#submit-btn');
                var originalText = submitBtn.html();

                // Disable submit button and show loading
                submitBtn.prop('disabled', true).html('<i class="ri-loader-4-line align-bottom me-1"></i> Menyimpan...');

                // Clear previous errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                window.location.href = '{{ route('admin.shifts.index') }}';
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(xhr) {
                        var response = xhr.responseJSON;

                        if (xhr.status === 422) {
                            // Validation errors
                            if (response.errors) {
                                $.each(response.errors, function(field, messages) {
                                    var input = $('[name="' + field + '"]');
                                    input.addClass('is-invalid');
                                    input.siblings('.invalid-feedback').text(messages[0]);
                                });
                            }
                        } else {
                            // Other errors
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Terjadi kesalahan saat menyimpan data.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    complete: function() {
                        // Re-enable submit button
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });
    </script>
@endpush

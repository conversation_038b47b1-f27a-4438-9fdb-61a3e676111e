<?php

namespace Database\Seeders;

use App\Models\ClassSchedule;
use App\Models\LessonHour;
use App\Models\TeacherAssignment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClassScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all teacher assignments and lesson hours
        $teacherAssignments = TeacherAssignment::where('is_homeroom_teacher', false)->get();
        $lessonHours = LessonHour::orderBy('sequence')->get();

        if ($teacherAssignments->isEmpty() || $lessonHours->isEmpty()) {
            $this->command->error('Teacher assignments or lesson hours not found. Please run their seeders first.');

            return;
        }

        // Clear existing schedules to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('class_schedules')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Days of the week
        $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        // Group assignments by classroom to avoid scheduling conflicts
        $assignmentsByClassroom = $teacherAssignments->groupBy('classroom_id');

        foreach ($assignmentsByClassroom as $assignments) {
            // Create a schedule for each day
            foreach ($daysOfWeek as $day) {
                // Shuffle assignments to randomize the schedule
                $shuffledAssignments = $assignments->shuffle();

                // Assign up to 8 lessons per day (or less if there are fewer assignments)
                $lessonCount = min(8, $shuffledAssignments->count());

                for ($i = 0; $i < $lessonCount; $i++) {
                    // Get the appropriate lesson hour
                    $lessonHour = $lessonHours->where('sequence', $i + 1)->first();
                    if (! $lessonHour) {
                        continue;
                    }

                    // Get the assignment for this slot
                    $assignment = $shuffledAssignments[$i];

                    ClassSchedule::create([
                        'teacher_assignment_id' => $assignment->id,
                        'lesson_hour_id' => $lessonHour->id,
                        'day_of_week' => $day,
                        'status' => 'active',
                    ]);
                }
            }
        }

        $this->command->info('Class schedules created successfully!');
    }
}

<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'nullable', 'integer', 'exists:teachers,id'],
            'subject_id' => ['sometimes', 'nullable', 'integer', 'exists:subjects,id'],
            'classroom_id' => ['sometimes', 'nullable', 'integer', 'exists:classrooms,id'],
            'academic_year_id' => ['sometimes', 'nullable', 'integer', 'exists:academic_years,id'],
            'is_homeroom_teacher' => ['sometimes', 'nullable', 'boolean'],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'Guru',
            'subject_id' => '<PERSON>',
            'classroom_id' => 'Kelas',
            'academic_year_id' => 'Tahun Akademik',
            'is_homeroom_teacher' => 'Jenis Penugasan',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'teacher_id.exists' => 'Guru yang dipilih tidak valid.',
            'subject_id.exists' => 'Mata pelajaran yang dipilih tidak valid.',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid.',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid.',
            'is_homeroom_teacher.boolean' => 'Jenis penugasan harus berupa nilai boolean.',
        ];
    }
}

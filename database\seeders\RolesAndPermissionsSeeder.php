<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // this can be done as separate statements
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'principal']);
        Role::create(['name' => 'treasurer']);
        Role::create(['name' => 'subject_teacher']);
        Role::create(['name' => 'substitute_teacher']);
        Role::create(['name' => 'student']);
    }
}

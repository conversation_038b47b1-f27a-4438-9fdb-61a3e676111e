<?php

namespace App\Http\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;

class StudentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'gender' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedGenders())],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
            'status' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedStatuses())],
            'classroom_id' => ['sometimes', 'nullable', 'integer', 'exists:classrooms,id'],
            'nis' => ['sometimes', 'nullable', 'string', 'max:20'],
            'nisn' => ['sometimes', 'nullable', 'string', 'max:20'],
        ];
    }

    /**
     * Get allowed genders for student filtering.
     */
    public function getAllowedGenders(): array
    {
        return GenderEnum::values();
    }

    /**
     * Get allowed statuses for student filtering.
     */
    public function getAllowedStatuses(): array
    {
        return UserStatus::values();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'gender' => 'Jenis kelamin',
            'search' => 'Pencarian',
            'status' => 'Status',
            'classroom_id' => 'Kelas',
            'nis' => 'NIS',
            'nisn' => 'NISN',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'gender.in' => 'Jenis kelamin yang dipilih tidak valid.',
            'status.in' => 'Status yang dipilih tidak valid.',
            'classroom_id.exists' => 'Kelas yang dipilih tidak ditemukan.',
            'nis.max' => 'NIS tidak boleh lebih dari 20 karakter.',
            'nisn.max' => 'NISN tidak boleh lebih dari 20 karakter.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Handle empty string as null for proper filter application
        $this->merge([
            'gender' => $this->gender === '' ? null : $this->gender,
            'status' => $this->status === '' ? null : $this->status,
            'search' => $this->search === '' ? null : $this->search,
            'classroom_id' => $this->classroom_id === '' ? null : $this->classroom_id,
            'nis' => $this->nis === '' ? null : $this->nis,
            'nisn' => $this->nisn === '' ? null : $this->nisn,
        ]);
    }
}

<?php

namespace App\Enums;

enum ClassroomLevelEnum: string
{
    case ONE = '1';
    case TWO = '2';
    case THREE = '3';
    case FOUR = '4';
    case FIVE = '5';
    case SIX = '6';
    case SEVEN = '7';
    case EIGHT = '8';
    case NINE = '9';
    case TEN = '10';
    case ELEVEN = '11';
    case TWELVE = '12';

    /**
     * Label untuk masing-masing kelas
     */
    public function label(): string
    {
        return match ($this) {
            self::ONE => 'Kelas 1',
            self::TWO => 'Kelas 2',
            self::THREE => 'Kelas 3',
            self::FOUR => 'Kelas 4',
            self::FIVE => 'Kelas 5',
            self::SIX => 'Kelas 6',
            self::SEVEN => 'Kelas 7',
            self::EIGHT => 'Kelas 8',
            self::NINE => 'Kelas 9',
            self::TEN => 'Kelas 10',
            self::ELEVEN => 'Kelas 11',
            self::TWELVE => 'Kelas 12',
        };
    }

    /**
     * Ambil semua value dalam bentuk array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Ambil semua value dan label untuk dropdown
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->value => $case->label()])
            ->toArray();
    }

    /**
     * Ambil label dari string value atau enum
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            return $value->label();
        }

        $enum = self::tryFrom($value);

        return $enum?->label() ?? ucfirst($value);
    }
}

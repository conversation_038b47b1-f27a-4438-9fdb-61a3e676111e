<?php

namespace App\Http\Requests\ClassScheduleRequests;

use Illuminate\Foundation\Http\FormRequest;

class ClassScheduleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'teacher_assignment_id' => 'required|exists:teacher_assignments,id',
            'lesson_hour_id' => 'required|exists:lesson_hours,id',
            'day_of_week' => 'required|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'status' => 'sometimes|string|in:active,inactive',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'teacher_assignment_id.required' => 'Guru dan mata pelajaran wajib dipilih',
            'teacher_assignment_id.exists' => 'Guru dan mata pelajaran yang dipilih tidak valid',
            'lesson_hour_id.required' => 'Jam pelajaran wajib dipilih',
            'lesson_hour_id.exists' => 'Jam pelajaran yang dipilih tidak valid',
            'day_of_week.required' => '<PERSON> wajib dipilih',
            'day_of_week.in' => 'Hari yang dipilih tidak valid',
            'status.in' => 'Status tidak valid',
        ];
    }
}

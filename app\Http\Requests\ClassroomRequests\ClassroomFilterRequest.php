<?php

namespace App\Http\Requests\ClassroomRequests;

use App\Enums\ClassroomLevelEnum;
use App\Enums\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ClassroomFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'program_id' => ['sometimes', 'nullable', 'integer', 'exists:programs,id'],
            'level' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedLevels())],
            'status' => ['sometimes', 'nullable', 'string', 'in:' . implode(',', $this->getAllowedStatuses())],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get allowed levels for classroom filtering.
     */
    public function getAllowedLevels(): array
    {
        return ClassroomLevelEnum::values();
    }

    /**
     * Get allowed statuses for classroom filtering.
     */
    public function getAllowedStatuses(): array
    {
        return StatusEnum::values();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'program_id' => 'Program',
            'level' => 'Level',
            'status' => 'Status',
            'search' => 'Pencarian',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'program_id.exists' => 'Program yang dipilih tidak ditemukan.',
            'level.in' => 'Level yang dipilih tidak valid.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }
}

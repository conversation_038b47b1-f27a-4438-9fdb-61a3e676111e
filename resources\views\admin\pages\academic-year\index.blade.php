@extends('admin.layouts.app')

@section('title', 'Tahun Akademik')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Tahun Akademik',
        'breadcrumb' => 'Manajemen Akademik',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-academic-years">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <a href="{{ route('admin.academic-years.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status-filter" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="status-filter">
                                    <option value="">Semua Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="semester-filter" class="form-label">Semester</label>
                                <select class="form-select" data-choices name="semester" id="semester-filter">
                                    <option value="">Semua Semester</option>
                                    @foreach ($semesters as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <!-- Search Input -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari tahun akademik..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr>
                                    <th>No</th>
                                    <th>Tahun Akademik</th>
                                    <th>Semester</th>
                                    <th>Tanggal Mulai</th>
                                    <th>Tanggal Selesai</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .dataTables_length,
        .dataTables_filter {
            display: none;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Inisialisasi DataTable untuk tabel tahun ajaran
            const academicYearsTable = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.academic-years.index') }}",
                    data: d => ({
                        ...d,
                        status: $('#status-filter').val(),
                        semester: $('#semester-filter').val(),
                        search: $('#search-input').val(),
                    }),
                    complete: response => {
                        const totalRecords = response.responseJSON?.recordsTotal || 0;
                        $('#total-academic-years').text(totalRecords);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data tahun ajaran.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'name',
                    name: 'name',
                }, {
                    data: 'semester',
                    name: 'semester',
                    orderable: true,
                }, {
                    data: 'start_date',
                    name: 'start_date',
                }, {
                    data: 'end_date',
                    name: 'end_date',
                }, {
                    data: 'status',
                    name: 'status',
                }, {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                }],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
                order: [
                    [1, 'asc']
                ],
                columnDefs: [{
                    orderable: false,
                    targets: [0, 6],
                }, {
                    className: "text-center",
                    targets: [0],
                }],
            });

            // Fungsi untuk memuat ulang tabel
            const reloadTable = () => academicYearsTable.draw();

            // Fungsi untuk menangani penghapusan data
            const handleDelete = url => {
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus tahun ajaran ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true,
                }).then(result => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: response => {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500,
                                });
                                reloadTable();
                            },
                            error: xhr => {
                                const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            };

            // Event Listeners
            // Memuat ulang tabel saat filter atau pencarian berubah
            $('#status-filter, #semester-filter').on('change', reloadTable);
            $('#search-button').on('click', reloadTable);
            $('#search-input').on('keyup', e => e.key === 'Enter' && reloadTable());

            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    academicYearsTable.columns.adjust().draw();
                }, 300);
            });

            // Handle delete button clicks using event delegation
            $('#datatable').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan.',
                        icon: 'error',
                    });
                    return;
                }
                handleDelete(url);
            });
        });
    </script>
@endpush

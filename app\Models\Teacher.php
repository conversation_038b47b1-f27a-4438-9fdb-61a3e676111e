<?php

namespace App\Models;

use App\Enums\GenderEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Teacher extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'birth_place',
        'birth_date',
        'gender',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
        'gender' => GenderEnum::class,
    ];

    /**
     * Get the user that owns the teacher.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the classrooms for the teacher.
     */
    public function classrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    /**
     * Get the teacher assignments for the teacher.
     */
    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    /**
     * Get the active teacher assignments for the teacher.
     */
    public function activeAssignments(): Has<PERSON>any
    {
        return $this->teacherAssignments()->whereHas('academicYear', function ($query) {
            $query->where('status', 'active');
        });
    }

    /**
     * Check if the teacher is a homeroom teacher for any classroom.
     */
    public function isHomeroomTeacher(): bool
    {
        return $this->teacherAssignments()
            ->where('is_homeroom_teacher', true)
            ->exists();
    }

    /**
     * Get the full name from the associated user.
     */
    public function getFullNameAttribute(): string
    {
        return $this->user->name ?? '';
    }

    /**
     * Get the email from the associated user.
     */
    public function getEmailAttribute(): string
    {
        return $this->user->email ?? '';
    }

    /**
     * Get the username from the associated user.
     */
    public function getUsernameAttribute(): string
    {
        return $this->user->username ?? '';
    }

    /**
     * Get the status from the associated user.
     */
    public function getStatusAttribute(): bool
    {
        return $this->user->status ?? false;
    }
}

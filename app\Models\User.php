<?php

namespace App\Models;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasRoles, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'name',
        'email',
        'password',
        'phone_number',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => UserStatus::class,
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get user's role as an enum.
     */
    public function roleEnum(): ?RoleEnum
    {
        $role = $this->getRoleNames()->first();

        return $role ? RoleEnum::from($role) : null;
    }

    /**
     * Get user's role as a human-readable label.
     */
    public function roleLabel(): string
    {
        return $this->roleEnum()?->label() ?? '-';
    }



    /**
     * Get the teacher profile associated with the user.
     */
    public function teacher(): HasOne
    {
        return $this->hasOne(Teacher::class);
    }

    /**
     * Get the student profile associated with the user.
     */
    public function student(): HasOne
    {
        return $this->hasOne(Student::class);
    }

    /**
     * Check if the user is a teacher.
     */
    public function isTeacher(): bool
    {
        return $this->hasRole([RoleEnum::SUBJECT_TEACHER->value, RoleEnum::SUBSTITUTE_TEACHER->value]);
    }

    /**
     * Check if the user is a student.
     */
    public function isStudent(): bool
    {
        return $this->hasRole(RoleEnum::STUDENT->value);
    }

    /**
     * Check if the user is staff (admin, principal, treasurer).
     */
    public function isStaff(): bool
    {
        return $this->hasRole([
            RoleEnum::ADMIN->value,
            RoleEnum::PRINCIPAL->value,
            RoleEnum::TREASURER->value
        ]);
    }

    /**
     * Get the user's profile based on their role.
     * Returns teacher or student profile if available.
     */
    public function getProfile(): Teacher|Student|null
    {
        if ($this->isTeacher() && $this->relationLoaded('teacher')) {
            return $this->teacher;
        }

        if ($this->isStudent() && $this->relationLoaded('student')) {
            return $this->student;
        }

        return null;
    }
}

@extends('admin.layouts.app')

@section('title', '<PERSON>')

@section('content')
    @include('admin.components.page-title', [
        'title' => '<PERSON>',
        'breadcrumb' => 'Man<PERSON><PERSON><PERSON>las',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-subjects">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <a href="{{ route('admin.subjects.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Program Dropdown -->
                        <div class="col-12 col-sm-6 col-md-4">
                            <div class="form-group">
                                <label for="filter-program" class="form-label">Program</label>
                                <select class="form-select" data-choices name="program_id" id="filter-program">
                                    <option value="">Semua Program</option>
                                    @foreach ($programs as $program)
                                        <option value="{{ $program->id }}">{{ $program->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-12 col-sm-6 col-md-4">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari mata pelajaran..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama Mata Pelajaran</th>
                                    <th>Program</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Inisialisasi DataTable untuk tabel mata pelajaran
            const subjectsTable = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.subjects.index') }}",
                    data: d => ({
                        ...d,
                        program_id: $('#filter-program').val(),
                        search: $('#search-input').val(),
                    }),
                    complete: response => {
                        const totalSubjects = response.responseJSON?.recordsTotal || 0;
                        $('#total-subjects').text(totalSubjects);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data mata pelajaran.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'name',
                    name: 'name',
                }, {
                    data: 'program',
                    name: 'program',
                }, {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                }],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya",
                    }
                },
                order: [
                    [1, 'asc']
                ], // Sort by name by default
            });

            // Fungsi untuk memuat ulang tabel
            const reloadTable = () => subjectsTable.draw();

            // Fungsi untuk menangani penghapusan data
            const handleDelete = url => {
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data mata pelajaran ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true,
                }).then(result => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: response => {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500,
                                });
                                reloadTable();
                            },
                            error: xhr => {
                                const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            };

            // Event Listeners
            // Memuat ulang tabel saat filter program atau pencarian berubah
            $('#filter-program').on('change', reloadTable);
            $('#search-button').on('click', reloadTable);
            $('#search-input').on('keyup', e => e.key === 'Enter' && reloadTable());

            // Menyesuaikan kolom tabel saat sidebar di-toggle
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    subjectsTable.columns.adjust().draw();
                }, 300);
            });

            // Handle delete button clicks using event delegation
            $('#datatable').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan.',
                        icon: 'error',
                    });
                    return;
                }
                handleDelete(url);
            });
        });
    </script>
@endpush

<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\StaffRequests\StaffStoreRequest;
use App\Http\Requests\StaffRequests\StaffFilterRequest;
use App\Http\Requests\StaffRequests\StaffUpdateRequest;

class StaffController extends Controller
{
    /**
     * Display a listing of staff members.
     */
    public function index(StaffFilterRequest $request): View|JsonResponse
    {
        $query = User::with('roles:id,name')
            ->whereHas('roles', function ($q) {
                $q->whereIn('name', RoleEnum::staff());
            })
            ->select(['id', 'name', 'email', 'phone_number', 'status', 'created_at']);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatStaffForDatatable($query);
        }

        return view('admin.pages.staff.index', [
            'statuses' => UserStatus::dropdown(),
            'roles' => RoleEnum::staffOptions(),
            'initialFilters' => $request->validated(), // Pass filters to view
        ]);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Filter by role
        if (!empty($filters['role'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm)
                    ->orWhere('phone_number', 'like', $searchTerm);
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables.
     */
    protected function formatStaffForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name ?? '-')
            ->editColumn('email', fn($row) => $row->email ?? '-')
            ->editColumn('phone_number', fn($row) => $row->phone_number ?? '-')
            ->editColumn('role', function ($row) {
                $role = $row->getRoleNames()->first();
                return RoleEnum::getLabel($role);
            })
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.staff._action', [
                    'edit' => route('admin.staff.edit', $row->id),
                    'destroy' => route('admin.staff.destroy', $row->id),
                    'id' => $row->id,
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new staff member.
     */
    public function create(): View
    {
        return view('admin.pages.staff.create', [
            'roles' => RoleEnum::staffOptions(),
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created staff member.
     */
    public function store(StaffStoreRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $staff = User::create([
            'username' => $validated['username'],
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone_number' => $validated['phone_number'] ?? null,
            'status' => $validated['status'],
            'password' => bcrypt($validated['password']),
        ]);

        $staff->assignRole($validated['role']);

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil dibuat.',
            'data' => $staff,
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the form for editing the specified staff member.
     */
    public function edit(int $id): View
    {
        $staff = User::with('roles:id,name')->findOrFail($id);

        return view('admin.pages.staff.edit', [
            'staff' => $staff,
            'roles' => RoleEnum::staffOptions(),
            'statuses' => UserStatus::dropdown(),
            'currentRole' => $staff->getRoleNames()->first(),
        ]);
    }

    /**
     * Update the specified staff member.
     */
    public function update(StaffUpdateRequest $request, int $id): JsonResponse
    {
        $staff = User::findOrFail($id);
        $validated = $request->validated();

        $updateData = [
            'username' => $validated['username'],
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone_number' => $validated['phone_number'] ?? null,
            'status' => $validated['status'],
        ];

        // Only update password if provided
        if (!empty($validated['password'])) {
            $updateData['password'] = bcrypt($validated['password']);
        }

        $staff->update($updateData);
        $staff->syncRoles($validated['role']);

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil diperbarui.',
        ]);
    }

    /**
     * Remove the specified staff member.
     */
    public function destroy(int $id): JsonResponse
    {
        $staff = User::findOrFail($id);

        // Prevent deleting yourself
        if ($staff->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus akun sendiri.',
            ], Response::HTTP_FORBIDDEN);
        }

        // prevent deleting active user
        if ($staff->status === UserStatus::Active) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus akun yang masih aktif.',
            ], Response::HTTP_FORBIDDEN);
        }

        // Prevent deleting the last active admin
        if (
            $staff->hasRole(RoleEnum::ADMIN->value) && $staff->status === UserStatus::Active && User::whereHas('roles', function ($q) {
                $q->where('name', RoleEnum::ADMIN->value);
            })->where('status', UserStatus::Active)->count() <= 1
        ) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus administrator terakhir yang aktif.',
            ], Response::HTTP_FORBIDDEN);
        }

        $staff->delete();

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil dihapus.',
        ]);
    }
}

@extends('admin.layouts.app')

@section('title', 'Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Guru',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            Daftar @yield('title')
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-teachers">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" data-action="export">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" data-action="import">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="{{ route('admin.teachers.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-3">
                            <label for="filter-status" class="form-label">Status</label>
                            <select class="form-select" data-choices name="status" id="filter-status">
                                <option value="">Semua Status</option>
                                @foreach ($statuses as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-role" class="form-label">Role</label>
                            <select class="form-select" data-choices name="role" id="filter-role">
                                <option value="">Semua Role</option>
                                @foreach ($roles as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                            <select class="form-select" data-choices name="gender" id="filter-gender">
                                <option value="">Semua Jenis Kelamin</option>
                                @foreach ($genders as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search-input" class="form-label">Cari</label>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Cari nama, email, tempat lahir..." id="search-input" name="search">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table id="teachers-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Tanggal Lahir</th>
                                    <th>Nomor Telp</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .dataTables_length,
        .dataTables_filter {
            display: none;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Inisialisasi DataTable untuk tabel guru
            const teachersTable = $('#teachers-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.teachers.index') }}",
                    data: d => {
                        d.status = $('#filter-status').val();
                        d.role = $('#filter-role').val();
                        d.gender = $('#filter-gender').val();
                        d.search = $('#search-input').val();
                    },
                    complete: response => {
                        const totalTeachers = response.responseJSON?.recordsTotal || 0;
                        $('#total-teachers').text(totalTeachers);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data guru.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'user.name',
                    name: 'user.name',
                }, {
                    data: 'user.email',
                    name: 'user.email',
                }, {
                    data: 'user.role',
                    name: 'user.role',
                }, {
                    data: 'gender',
                    name: 'gender',
                }, {
                    data: 'birth_date',
                    name: 'birth_date',
                }, {
                    data: 'user.phone_number',
                    name: 'user.phone_number',
                }, {
                    data: 'status',
                    name: 'status',
                }, {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                }],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: 'Cari...',
                    lengthMenu: 'Tampilkan _MENU_ data',
                    zeroRecords: 'Data tidak ditemukan',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                    infoFiltered: '(disaring dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya',
                    }
                },
                order: [
                    [1, 'asc']
                ] // Sort by name by default
            });

            // Fungsi untuk memuat ulang tabel
            const reloadTable = () => teachersTable.draw();

            // Fungsi untuk menampilkan pesan "Coming Soon"
            const showComingSoon = () => {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur ini akan segera tersedia.',
                    icon: 'info',
                });
            };

            // Fungsi untuk menangani penghapusan data
            const handleDelete = url => {
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data guru ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true,
                }).then(result => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: response => {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500,
                                });
                                reloadTable();
                            },
                            error: xhr => {
                                const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            };

            // Event Listeners
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    teachersTable.columns.adjust().draw();
                }, 300);
            });

            $('#filter-status, #filter-role, #filter-gender').change(reloadTable);
            $('#search-button').click(reloadTable);
            $('#search-input').keyup(e => e.key === 'Enter' && reloadTable());

            $('#export-btn, #import-btn').click(showComingSoon);

            $('#teachers-table').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan.',
                        icon: 'error',
                    });
                    return;
                }
                handleDelete(url);
            });
        });
    </script>
@endpush

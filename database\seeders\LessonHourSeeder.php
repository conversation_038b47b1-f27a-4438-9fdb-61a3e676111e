<?php

namespace Database\Seeders;

use App\Models\LessonHour;
use Illuminate\Database\Seeder;

class LessonHourSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Morning lesson hours
        $morningLessonHours = [
            ['name' => 'Jam ke-1', 'start_time' => '07:00:00', 'end_time' => '07:45:00', 'sequence' => 1],
            ['name' => 'Jam ke-2', 'start_time' => '07:45:00', 'end_time' => '08:30:00', 'sequence' => 2],
            ['name' => 'Jam ke-3', 'start_time' => '08:30:00', 'end_time' => '09:15:00', 'sequence' => 3],
            ['name' => 'Istirahat 1', 'start_time' => '09:15:00', 'end_time' => '09:30:00', 'sequence' => 4],
            ['name' => 'Jam ke-4', 'start_time' => '09:30:00', 'end_time' => '10:15:00', 'sequence' => 5],
            ['name' => 'Jam ke-5', 'start_time' => '10:15:00', 'end_time' => '11:00:00', 'sequence' => 6],
            ['name' => 'Jam ke-6', 'start_time' => '11:00:00', 'end_time' => '11:45:00', 'sequence' => 7],
            ['name' => 'Istirahat 2', 'start_time' => '11:45:00', 'end_time' => '12:15:00', 'sequence' => 8],
        ];

        foreach ($morningLessonHours as $lessonHour) {
            LessonHour::create($lessonHour);
        }
    }
}

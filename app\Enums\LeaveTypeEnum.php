<?php

namespace App\Enums;

enum LeaveTypeEnum: string
{
    case SICK = 'sick';
    case OFFICIAL_DUTY = 'official_duty';
    case PERSONAL = 'personal';
    case OTHER = 'other';

    /**
     * Get all leave type values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all leave types as key-value pairs (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::SICK->value => 'Cuti Sakit',
            self::OFFICIAL_DUTY->value => 'Tugas Dinas',
            self::PERSONAL->value => 'Keperluan Pribadi',
            self::OTHER->value => 'Lainnya',
        ];
    }

    /**
     * Get the display label for a leave type value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get color class for the leave type
     */
    public function color(): string
    {
        return match ($this) {
            self::SICK => 'danger',
            self::OFFICIAL_DUTY => 'primary',
            self::PERSONAL => 'warning',
            self::OTHER => 'secondary',
        };
    }

    /**
     * Get icon for the leave type
     */
    public function icon(): string
    {
        return match ($this) {
            self::SICK => 'ri-heart-pulse-line',
            self::OFFICIAL_DUTY => 'ri-briefcase-line',
            self::PERSONAL => 'ri-user-line',
            self::OTHER => 'ri-more-line',
        };
    }
}
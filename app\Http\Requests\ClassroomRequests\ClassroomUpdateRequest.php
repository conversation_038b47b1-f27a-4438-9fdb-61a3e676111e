<?php

namespace App\Http\Requests\ClassroomRequests;

use App\Enums\ClassroomLevelEnum;
use App\Enums\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ClassroomUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100'],
            'level' => ['required', 'string', 'max:50'],
            'capacity' => ['required', 'integer', 'min:1', 'max:100'],
            'program_id' => ['required', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['required', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'description' => ['nullable', 'string', 'max:500'],
            'status' => ['required', 'string', 'in:' . implode(',', StatusEnum::values())],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama Kelas',
            'level' => 'Level',
            'capacity' => 'Kapasitas',
            'program_id' => 'Program',
            'shift_id' => 'Shift',
            'teacher_id' => 'Guru',
            'academic_year_id' => 'Tahun Akademik',
            'description' => 'Deskripsi',
            'status' => 'Status',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama kelas wajib diisi.',
            'name.string' => 'Nama kelas harus berupa teks.',
            'name.max' => 'Nama kelas tidak boleh lebih dari 100 karakter.',
            'level.required' => 'Level kelas wajib dipilih.',
            'level.string' => 'Level kelas harus berupa teks.',
            'level.in' => 'Level kelas yang dipilih tidak valid.',
            'capacity.required' => 'Kapasitas kelas wajib diisi.',
            'capacity.integer' => 'Kapasitas kelas harus berupa angka.',
            'capacity.min' => 'Kapasitas kelas minimal 1 siswa.',
            'capacity.max' => 'Kapasitas kelas maksimal 100 siswa.',
            'program_id.required' => 'Program wajib dipilih.',
            'program_id.exists' => 'Program yang dipilih tidak ditemukan.',
            'shift_id.exists' => 'Shift yang dipilih tidak ditemukan.',
            'teacher_id.required' => 'Guru wajib dipilih.',
            'teacher_id.exists' => 'Guru yang dipilih tidak ditemukan.',
            'academic_year_id.required' => 'Tahun akademik wajib dipilih.',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak ditemukan.',
            'description.string' => 'Deskripsi harus berupa teks.',
            'description.max' => 'Deskripsi tidak boleh lebih dari 500 karakter.',
            'status.required' => 'Status wajib dipilih.',
            'status.string' => 'Status harus berupa teks.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }
}

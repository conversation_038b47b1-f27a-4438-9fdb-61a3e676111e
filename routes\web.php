<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DisplayController;
use App\Http\Controllers\Admin\AcademicYearController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AttendanceController;
use App\Http\Controllers\Admin\ClassroomController;
use App\Http\Controllers\Admin\ClassScheduleController;
use App\Http\Controllers\Admin\LeaveRequestController;
use App\Http\Controllers\Admin\LessonHourController;
use App\Http\Controllers\Admin\ProgramController;
use App\Http\Controllers\Admin\ShiftController;
use App\Http\Controllers\Admin\StaffController;
use App\Http\Controllers\Admin\StudentController;
use App\Http\Controllers\Admin\SubjectController;
use App\Http\Controllers\Admin\TeacherAssignmentController;
use App\Http\Controllers\Admin\TeacherController;

/*
|--------------------------------------------------------------------------
| Authentication Routes (sementara)
|--------------------------------------------------------------------------
*/
Auth::loginUsingId(1); // Hati-hati, hanya untuk debugging/testing

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
*/
Route::get('/display/tv', [DisplayController::class, 'tvDisplay'])->name('display.tv');

/*
|--------------------------------------------------------------------------
| Admin Routes (auth required)
|--------------------------------------------------------------------------
*/
Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminController::class, 'index'])->name('dashboard');

    // Staff
    Route::resource('staff', StaffController::class)->except('show');

    // Teacher
    Route::resource('teachers', TeacherController::class)->except('show');

    // Student
    Route::resource('students', StudentController::class)->except('show');
    Route::prefix('students')->name('students.')->group(function () {
        Route::post('import', [StudentController::class, 'import'])->name('import');
        Route::get('export', [StudentController::class, 'export'])->name('export');
        Route::get('template', [StudentController::class, 'template'])->name('template');
    });

    // Program
    Route::resource('programs', ProgramController::class)->except('show');

    // Subject
    Route::resource('subjects', SubjectController::class)->except('show');

    // Lesson Hour
    Route::resource('lesson-hours', LessonHourController::class)->except('show');

    // Teacher Assignment
    Route::resource('teacher-assignments', TeacherAssignmentController::class)->except('show');
    Route::prefix('teacher-assignments')->name('teacher-assignments.')->group(function () {
        Route::get('statistics', [TeacherAssignmentController::class, 'getStatistics'])->name('statistics');
        Route::get('subjects-by-program/{program}', [TeacherAssignmentController::class, 'getSubjectsByProgram'])->name('subjects-by-program');
        Route::get('classrooms-by-program/{program}', [TeacherAssignmentController::class, 'getClassroomsByProgram'])->name('classrooms-by-program');
        Route::get('check-availability', [TeacherAssignmentController::class, 'checkAssignmentAvailability'])->name('check-availability');
        Route::delete('bulk-destroy', [TeacherAssignmentController::class, 'bulkDestroy'])->name('bulk-destroy');
    });

    // Class Schedule
    Route::resource('class-schedules', ClassScheduleController::class)->except('show');

    // Academic Year
    Route::resource('academic-years', AcademicYearController::class)->except('show');

    // Classroom
    Route::resource('classrooms', ClassroomController::class)->except('show');

    // Shift
    Route::resource('shifts', ShiftController::class)->except('show');

    // Attendance
    Route::resource('attendances', AttendanceController::class)->except('show');

    // Leave Request
    Route::resource('leave-requests', LeaveRequestController::class);
});


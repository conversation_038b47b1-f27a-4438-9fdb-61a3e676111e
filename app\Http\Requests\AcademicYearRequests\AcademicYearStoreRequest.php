<?php

namespace App\Http\Requests\AcademicYearRequests;

use Illuminate\Validation\Rule;
use App\Enums\AcademicSemesterEnum;
use App\Enums\AcademicYearStatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class AcademicYearStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'semester' => ['required', 'string', Rule::in(AcademicSemesterEnum::values())],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'status' => ['required', 'string', Rule::in(AcademicYearStatusEnum::values())],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama tahun akademik wajib diisi.',
            'semester.required' => 'Semester wajib dipilih.',
            'semester.in' => 'Semester yang dipilih tidak valid.',
            'start_date.required' => 'Tanggal mulai wajib diisi.',
            'start_date.date' => 'Format tanggal mulai tidak valid.',
            'end_date.required' => 'Tanggal selesai wajib diisi.',
            'end_date.date' => 'Format tanggal selesai tidak valid.',
            'end_date.after' => 'Tanggal selesai harus setelah tanggal mulai.',
            'status.required' => 'Status wajib dipilih.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }

    /**
     * Get custom attributes for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Tahun Akademik',
            'semester' => 'Semester',
            'start_date' => 'Tanggal Mulai',
            'end_date' => 'Tanggal Selesai',
            'status' => 'Status',
        ];
    }
}

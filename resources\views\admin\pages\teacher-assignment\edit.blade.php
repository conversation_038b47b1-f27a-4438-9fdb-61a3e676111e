@extends('admin.layouts.app')

@section('title', 'Edit Penugasan Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Penugasan Guru',
        'breadcrumb' => 'Manajemen Akademik',
        'breadcrumb_items' => [
            [
                'text' => 'Penugasan Guru',
                'url' => route('admin.teacher-assignments.index'),
            ],
            [
                'text' => 'Edit Penugasan Guru',
                'url' => null,
            ],
        ],
    ])

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Penugasan Guru</h5>
                </div>
                <div class="card-body">
                    <form id="edit-teacher-assignment-form" action="{{ route('admin.teacher-assignments.update', $teacherAssignment->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="teacher_id" class="form-label">Guru <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="teacher_id" name="teacher_id" required>
                                    <option value="">Pilih Guru</option>
                                    @foreach ($teachers as $teacher)
                                        <option value="{{ $teacher->id }}" {{ $teacherAssignment->teacher_id == $teacher->id ? 'selected' : '' }}>
                                            {{ $teacher->user->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="teacher_id"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="classroom_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="classroom_id" name="classroom_id" required>
                                    <option value="">Pilih Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}" {{ $teacherAssignment->classroom_id == $classroom->id ? 'selected' : '' }}>
                                            {{ $classroom->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="classroom_id"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="subject_id" class="form-label">Mata Pelajaran</label>
                                <select class="form-select" data-choices id="subject_id" name="subject_id">
                                    <option value="">Pilih Mata Pelajaran</option>
                                    @foreach ($subjects as $subject)
                                        <option value="{{ $subject->id }}" {{ $teacherAssignment->subject_id == $subject->id ? 'selected' : '' }}>
                                            {{ $subject->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="subject_id"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                    <option value="">Pilih Tahun Akademik</option>
                                    @foreach ($academicYears as $academicYear)
                                        <option value="{{ $academicYear->id }}" {{ $teacherAssignment->academic_year_id == $academicYear->id ? 'selected' : '' }}>
                                            {{ $academicYear->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="academic_year_id"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_homeroom_teacher" name="is_homeroom_teacher" value="1" {{ $teacherAssignment->is_homeroom_teacher ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_homeroom_teacher">
                                    Jadikan sebagai Wali Kelas
                                </label>
                                <div class="form-text">Centang jika guru akan menjadi wali kelas untuk kelas ini</div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="ri-save-line align-bottom me-1"></i> Update
                            </button>
                            <a href="{{ route('admin.teacher-assignments.index') }}" class="btn btn-light">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Penugasan</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Guru:</label>
                        <p class="mb-0">{{ $teacherAssignment->teacher->user->name ?? '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Kelas:</label>
                        <p class="mb-0">{{ $teacherAssignment->classroom->name ?? '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Mata Pelajaran:</label>
                        <p class="mb-0">{{ $teacherAssignment->subject->name ?? '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Tahun Akademik:</label>
                        <p class="mb-0">{{ $teacherAssignment->academicYear->name ?? '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Jenis Penugasan:</label>
                        <span class="badge bg-{{ $teacherAssignment->is_homeroom_teacher ? 'success' : 'primary' }}">
                            {{ $teacherAssignment->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            $('#edit-teacher-assignment-form').on('submit', function(e) {
                e.preventDefault();

                const $form = $(this);
                const $submitBtn = $('#submit-btn');
                const originalText = $submitBtn.html();

                // Reset validation
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // Disable submit button
                $submitBtn.prop('disabled', true).html('<i class="ri-loader-line spin"></i> Menyimpan...');

                $.ajax({
                    url: $form.attr('action'),
                    type: 'POST',
                    data: $form.serialize(),
                    success: function(response) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            showConfirmButton: false,
                            timer: 1500,
                        }).then(() => {
                            window.location.href = '{{ route('admin.teacher-assignments.index') }}';
                        });
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;

                        if (response.errors) {
                            // Display validation errors
                            Object.keys(response.errors).forEach(field => {
                                const $field = $(`[name="${field}"]`);
                                const $feedback = $(`[data-field="${field}"]`);

                                $field.addClass('is-invalid');
                                $feedback.text(response.errors[field][0]);
                            });
                        } else {
                            // Display general error
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Terjadi kesalahan saat menyimpan data.',
                                icon: 'error',
                            });
                        }
                    },
                    complete: function() {
                        // Re-enable submit button
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });
    </script>
@endpush

<?php

namespace App\Http\Requests\AttendanceRequests;

use App\Enums\AttendanceStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttendanceUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'attendance_status' => ['required', Rule::in(AttendanceStatusEnum::values())],
            'notes' => 'nullable|string|max:500',
            'longitude' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'attendance_status' => 'Status Kehadiran',
            'notes' => 'Catatan',
            'longitude' => 'Longitude',
            'latitude' => 'Latitude',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'attendance_status.required' => 'Status kehadiran harus dipilih',
            'attendance_status.in' => 'Status kehadiran tidak valid',
            'notes.max' => 'Catatan maksimal 500 karakter',
            'longitude.numeric' => 'Longitude harus berupa angka',
            'latitude.numeric' => 'Latitude harus berupa angka',
        ];
    }
}

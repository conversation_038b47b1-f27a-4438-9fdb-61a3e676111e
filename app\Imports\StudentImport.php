<?php

namespace App\Imports;

use App\Enums\GenderEnum;
use App\Enums\ReligionEnum;
use App\Enums\UserStatus;
use App\Models\Student;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class StudentImport implements ToCollection, WithHeadingRow, WithValidation
{
    /**
     * @param Collection $rows
     */
    public function collection(Collection $rows)
    {
        DB::beginTransaction();

        try {
            foreach ($rows as $row) {
                $user = User::create([
                    'name' => $row['nama'],
                    'email' => $row['email'],
                    'password' => Hash::make($row['nisn']), // Use NISN as default password
                    'status' => UserStatus::ACTIVE,
                    'role' => 'student',
                ]);

                Student::create([
                    'user_id' => $user->id,
                    'nis' => $row['nis'],
                    'nisn' => $row['nisn'],
                    'gender' => strtolower($row['jenis_kelamin']) === 'laki-laki' ? GenderEnum::MALE : GenderEnum::FEMALE,
                    'birth_place' => $row['tempat_lahir'],
                    'birth_date' => \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['tanggal_lahir'])->format('Y-m-d'),
                    'religion' => ReligionEnum::fromLabel($row['agama']),
                    'address' => $row['alamat'],
                    'phone' => $row['telepon'],
                    'father_name' => $row['nama_ayah'] ?? null,
                    'mother_name' => $row['nama_ibu'] ?? null,
                    'parent_phone' => $row['telepon_ortu'] ?? null,
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function rules(): array
    {
        return [
            'nama' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email'),
            ],
            'nis' => [
                'required',
                'string',
                'max:20',
                Rule::unique('students', 'nis'),
            ],
            'nisn' => [
                'required',
                'string',
                'max:20',
                Rule::unique('students', 'nisn'),
            ],
            'jenis_kelamin' => ['required', 'string', Rule::in(['Laki-laki', 'Perempuan'])],
            'tempat_lahir' => 'required|string|max:100',
            'tanggal_lahir' => 'required',
            'agama' => ['required', 'string', Rule::in(ReligionEnum::labels())],
            'alamat' => 'required|string',
            'telepon' => 'nullable|string|max:20',
            'nama_ayah' => 'nullable|string|max:255',
            'nama_ibu' => 'nullable|string|max:255',
            'telepon_ortu' => 'nullable|string|max:20',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'nama.required' => 'Nama siswa wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah digunakan',
            'nis.required' => 'NIS wajib diisi',
            'nis.unique' => 'NIS sudah digunakan',
            'nisn.required' => 'NISN wajib diisi',
            'nisn.unique' => 'NISN sudah digunakan',
            'jenis_kelamin.required' => 'Jenis kelamin wajib diisi',
            'jenis_kelamin.in' => 'Jenis kelamin harus Laki-laki atau Perempuan',
            'tempat_lahir.required' => 'Tempat lahir wajib diisi',
            'tanggal_lahir.required' => 'Tanggal lahir wajib diisi',
            'agama.required' => 'Agama wajib diisi',
            'agama.in' => 'Agama tidak valid',
            'alamat.required' => 'Alamat wajib diisi',
        ];
    }
}

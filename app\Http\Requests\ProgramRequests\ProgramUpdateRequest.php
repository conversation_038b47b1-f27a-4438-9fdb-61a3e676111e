<?php

namespace App\Http\Requests\ProgramRequests;

use Illuminate\Foundation\Http\FormRequest;

class ProgramUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:programs,name,' . $this->program,
            'code' => 'nullable|string|max:20|unique:programs,code,' . $this->program,
            'description' => 'nullable|string|max:1000',
            'status' => 'sometimes|string|in:active,inactive',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama program wajib diisi.',
            'name.unique' => 'Nama program sudah digunakan.',
            'code.unique' => 'Kode program sudah digunakan.',
            'code.max' => 'Kode program maksimal 20 karakter.',
            'description.max' => 'Deskripsi maksimal 1000 karakter.',
            'status.in' => 'Status yang dipilih tidak valid.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama Program',
            'code' => 'Kode Program',
            'description' => 'Deskripsi',
            'status' => 'Status',
        ];
    }
}

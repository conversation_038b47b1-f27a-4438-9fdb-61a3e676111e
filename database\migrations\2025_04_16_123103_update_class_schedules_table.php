<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('class_schedules', function (Blueprint $table) {
            // Change status from boolean to enum
            $table->dropColumn('status');
            $table->enum('status', ['active', 'inactive'])->default('active')->after('day_of_week');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('class_schedules', function (Blueprint $table) {
            // Revert status from enum to boolean
            $table->dropColumn('status');
            $table->boolean('status')->default(true)->after('day_of_week');
        });
    }
};

<?php

namespace App\Enums;

enum UserStatus: int
{
    case Active = 1;   // representasi dari true
    case Inactive = 0; // representasi dari false

    public function label(): string
    {
        return match ($this) {
            self::Active => 'Aktif',
            self::Inactive => 'Tidak Aktif',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Active => 'success',
            self::Inactive => 'danger',
        };
    }

    public static function fromBool(bool $value): self
    {
        return $value ? self::Active : self::Inactive;
    }

    public function toBool(): bool
    {
        return $this === self::Active;
    }

    public function value(): int
    {
        return $this->value;
    }

    /**
     * Get all status values as an array (for database usage).
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all roles as key-value pairs (for dropdowns).
     */
    public static function dropdown(): array
    {
        return [
            self::Active->value => self::Active->label(),
            self::Inactive->value => self::Inactive->label(),
        ];
    }
}

<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

/**
 * Global exception handler for the application.
 *
 * Error Handling Policy:
 * 1. Never expose detailed error messages to clients in production
 * 2. Log all exceptions with appropriate context for debugging
 * 3. Return standardized error responses for API requests
 * 4. Handle different types of exceptions with appropriate status codes
 *
 * @see \App\Helpers\ErrorHandler For standardized error handling methods
 * @see \App\Traits\ApiResponseTrait::executeApiAction For standardized API error handling
 */
class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $exception) {
            // Log critical exceptions that are not handled elsewhere
            if ($exception instanceof \Error || $exception instanceof \ErrorException) {
                Log::critical('Critical error: '.$exception->getMessage(), [
                    'exception' => get_class($exception),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString(),
                ]);
            }
        });

        // Handle API exceptions differently
        $this->renderable(function (Throwable $exception, Request $request) {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->handleApiException($exception, $request);
            }
        });
    }

    /**
     * Handle API exceptions and return standardized JSON responses
     */
    private function handleApiException(Throwable $exception, Request $request): JsonResponse
    {
        // Default error message and status code
        $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
        $response = [
            'success' => false,
            'message' => 'Server Error',
        ];

        // Handle different types of exceptions
        if ($exception instanceof ValidationException) {
            $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY;
            $response['message'] = 'Validation Error';
            $response['errors'] = $exception->validator->errors()->toArray();
        } elseif ($exception instanceof ModelNotFoundException) {
            $statusCode = Response::HTTP_NOT_FOUND;
            $response['message'] = 'Resource not found';
        } elseif ($exception instanceof NotFoundHttpException) {
            $statusCode = Response::HTTP_NOT_FOUND;
            $response['message'] = 'The requested URL was not found';
        } elseif ($exception instanceof AuthorizationException) {
            $statusCode = Response::HTTP_FORBIDDEN;
            $response['message'] = 'You are not authorized to perform this action';
        } elseif ($exception instanceof HttpException) {
            $statusCode = $exception->getStatusCode();
            $response['message'] = $exception->getMessage() ?: 'HTTP Error';
        } elseif ($exception instanceof BusinessLogicException) {
            $statusCode = Response::HTTP_BAD_REQUEST;
            $response['message'] = $exception->getMessage();
        } elseif ($exception instanceof DatabaseException) {
            $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
            $response['message'] = 'Database Error';
            // Log the actual error but don't expose it
        }

        // Log the exception for debugging purposes
        if (
            ! ($exception instanceof ValidationException) &&
            ! ($exception instanceof BusinessLogicException) &&
            ! ($exception instanceof NotFoundException)
        ) {
            Log::error('API Exception: '.$exception->getMessage(), [
                'exception' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'request' => $request->all(),
            ]);
        }

        // For other exceptions, use the exception message in development only
        if (config('app.debug') && ! isset($response['errors'])) {
            $response['debug'] = [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        return response()->json($response, $statusCode);
    }
}

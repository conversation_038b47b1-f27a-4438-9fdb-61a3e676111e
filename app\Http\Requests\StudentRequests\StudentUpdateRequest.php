<?php

namespace App\Http\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use App\Models\Student;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('student');
        $student = Student::find($studentId);
        $userId = $student ? $student->user_id : 0; // Use 0 as fallback to avoid unique constraint issues

        return [
            // User data
            'name' => ['sometimes', 'string', 'max:255'],
            'username' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                'alpha_dash',
                Rule::unique('users', 'username')->ignore($userId),
            ],
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'password' => ['sometimes', 'nullable', 'string', Password::defaults()],
            'phone_number' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
            'status' => ['sometimes', Rule::enum(UserStatus::class)],

            // Student profile data
            'nis' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nis')->ignore($studentId),
            ],
            'nisn' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nisn')->ignore($studentId),
            ],
            'birth_place' => ['sometimes', 'string', 'max:100'],
            'birth_date' => ['sometimes', 'date', 'before:today'],
            'gender' => ['sometimes', Rule::enum(GenderEnum::class)],
            'parent_name' => ['sometimes', 'string', 'max:255'],
            'parent_phone' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama lengkap',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'phone_number' => 'Nomor telepon',
            'status' => 'Status',
            'nis' => 'NIS',
            'nisn' => 'NISN',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
            'parent_name' => 'Nama orang tua/wali',
            'parent_phone' => 'Nomor telepon orang tua/wali',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.string' => 'Nama lengkap harus berupa teks.',
            'name.max' => 'Nama lengkap tidak boleh lebih dari 255 karakter.',
            'username.string' => 'Username harus berupa teks.',
            'username.max' => 'Username tidak boleh lebih dari 100 karakter.',
            'username.unique' => 'Username sudah digunakan.',
            'username.alpha_dash' => 'Username hanya boleh berisi huruf, angka, tanda hubung, dan garis bawah.',
            'email.string' => 'Email harus berupa teks.',
            'email.email' => 'Email harus berupa alamat email yang valid.',
            'email.max' => 'Email tidak boleh lebih dari 255 karakter.',
            'email.unique' => 'Email sudah digunakan.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password harus memiliki panjang minimal :min karakter.',
            'phone_number.string' => 'Nomor telepon harus berupa teks.',
            'phone_number.max' => 'Nomor telepon tidak boleh lebih dari 20 karakter.',
            'phone_number.regex' => 'Nomor telepon hanya boleh berisi angka, tanda plus, tanda hubung, spasi, atau tanda kurung.',
            'status.enum' => 'Status yang dipilih tidak valid.',
            'nis.string' => 'NIS harus berupa teks.',
            'nis.max' => 'NIS tidak boleh lebih dari 20 karakter.',
            'nis.unique' => 'NIS sudah digunakan.',
            'nisn.string' => 'NISN harus berupa teks.',
            'nisn.max' => 'NISN tidak boleh lebih dari 20 karakter.',
            'nisn.unique' => 'NISN sudah digunakan.',
            'birth_place.string' => 'Tempat lahir harus berupa teks.',
            'birth_place.max' => 'Tempat lahir tidak boleh lebih dari 100 karakter.',
            'birth_date.date' => 'Tanggal lahir harus berupa tanggal yang valid.',
            'birth_date.before' => 'Tanggal lahir harus sebelum hari ini.',
            'gender.enum' => 'Jenis kelamin yang dipilih tidak valid.',
            'parent_name.string' => 'Nama orang tua/wali harus berupa teks.',
            'parent_name.max' => 'Nama orang tua/wali tidak boleh lebih dari 255 karakter.',
            'parent_phone.string' => 'Nomor telepon orang tua/wali harus berupa teks.',
            'parent_phone.max' => 'Nomor telepon orang tua/wali tidak boleh lebih dari 20 karakter.',
            'parent_phone.regex' => 'Nomor telepon orang tua/wali hanya boleh berisi angka, tanda plus, tanda hubung, spasi, atau tanda kurung.',
        ];
    }
}

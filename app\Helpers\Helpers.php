<?php

use Illuminate\Support\Facades\Route;

if (! function_exists('is_active')) {
    function is_active($patterns, string $return = '', bool $wildcard = false): string
    {
        $currentRoute = Route::currentRouteName();
        $currentPath = request()->path();

        foreach ((array) $patterns as $pattern) {
            // Match by route name
            if (
                $currentRoute && (
                    $currentRoute === $pattern ||
                    ($wildcard && str_starts_with($currentRoute, rtrim($pattern, '.')))
                )
            ) {
                return $return;
            }

            // Match by URL path
            if (
                $currentPath === ltrim($pattern, '/') ||
                ($wildcard && str_starts_with($currentPath, ltrim(rtrim($pattern, '*'), '/')))
            ) {
                return $return;
            }
        }

        return '';
    }
}

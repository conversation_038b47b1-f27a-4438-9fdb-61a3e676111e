<?php

namespace App\Http\Requests\ClassScheduleRequests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ClassScheduleUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'teacher_assignment_id' => [
                'required',
                Rule::exists('teacher_assignments', 'id')->where(function ($query) {
                    $query->where('classroom_id', $this->classroom_id)
                          ->where('academic_year_id', $this->academic_year_id);
                }),
            ],
            'lesson_hour_id' => 'required|exists:lesson_hours,id',
            'day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'classroom_id' => 'required|exists:classrooms,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'status' => 'sometimes|in:active,inactive',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'teacher_assignment_id.required' => '<PERSON> dan mata pelajaran wajib dipilih',
            'teacher_assignment_id.exists' => 'Guru dan mata pelajaran tidak valid untuk kelas dan tahun akademik ini',
            'lesson_hour_id.required' => 'Jam pelajaran wajib dipilih',
            'lesson_hour_id.exists' => 'Jam pelajaran yang dipilih tidak valid',
            'day_of_week.required' => 'Hari wajib dipilih',
            'day_of_week.in' => 'Hari yang dipilih tidak valid',
            'classroom_id.required' => 'Kelas wajib dipilih',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid',
            'academic_year_id.required' => 'Tahun akademik wajib dipilih',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid',
            'status.in' => 'Status tidak valid',
        ];
    }
}

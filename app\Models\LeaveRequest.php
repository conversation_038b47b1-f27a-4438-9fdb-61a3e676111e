<?php

namespace App\Models;

use App\Enums\LeaveStatusEnum;
use App\Enums\LeaveTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeaveRequest extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'teacher_id',
        'leave_date',
        'leave_type',
        'reason',
        'attachment_path',
        'status',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'leave_date' => 'date',
        'approved_at' => 'datetime',
        'leave_type' => LeaveTypeEnum::class,
        'status' => LeaveStatusEnum::class,
    ];

    /**
     * Get the teacher that owns the leave request.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the user who approved the leave request.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the tasks for this leave request.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(LeaveTask::class);
    }
}

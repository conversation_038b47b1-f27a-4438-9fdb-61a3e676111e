<?php

namespace App\Http\Controllers\Admin;

use Throwable;
use App\Models\Program;
use App\Enums\StatusEnum;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\ProgramRequests\ProgramStoreRequest;
use App\Http\Requests\ProgramRequests\ProgramFilterRequest;
use App\Http\Requests\ProgramRequests\ProgramUpdateRequest;

class ProgramController extends Controller
{
    /**
     * Display a listing of the programs.
     */
    public function index(ProgramFilterRequest $request): View|JsonResponse
    {
        $query = Program::withCount(['classrooms', 'subjects']);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatProgramsForDatatable($query);
        }

        return view('admin.pages.program.index', [
            'statuses' => StatusEnum::options(),
            'initialFilters' => $request->validated(), // Pass filters to view
        ]);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('code', 'like', $searchTerm)
                    ->orWhere('description', 'like', $searchTerm);
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format programs for DataTable
     */
    protected function formatProgramsForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', function ($row) {
                return $row->name;
            })
            ->editColumn('code', function ($row) {
                return $row->code ?? '-';
            })
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->addColumn('classrooms_count', function ($row) {
                return $row->classrooms_count;
            })
            ->addColumn('subjects_count', function ($row) {
                return $row->subjects_count;
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.program._action', [
                    'id' => $row->id,
                    'edit' => route('admin.programs.edit', $row->id),
                    'destroy' => route('admin.programs.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new program.
     */
    public function create(): View
    {
        return view('admin.pages.program.create', [
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created program
     */
    public function store(ProgramStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            // Generate unique code if not provided
            if (empty($validated['code'])) {
                $validated['code'] = 'PROG-' . time();
            }

            $program = Program::create([
                'name' => $validated['name'],
                'code' => $validated['code'],
                'description' => $validated['description'] ?? null,
                'status' => $validated['status'] ?? 'active',
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Program berhasil dibuat.',
                'data' => $program,
            ], Response::HTTP_CREATED);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat program.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified program.
     */
    public function show(Program $program): View
    {
        $program->load(['classrooms', 'subjects']);

        return view('admin.pages.program.show', [
            'program' => $program,
            'classrooms' => $program->classrooms,
        ]);
    }

    /**
     * Show the form for editing the specified program.
     */
    public function edit(Program $program): View
    {
        return view('admin.pages.program.edit', [
            'program' => $program,
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Update the specified program
     */
    public function update(ProgramUpdateRequest $request, Program $program): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $program->update([
                'name' => $validated['name'],
                'code' => $validated['code'] ?? $program->code,
                'description' => $validated['description'] ?? null,
                'status' => $validated['status'] ?? $program->status,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Program berhasil diperbarui.',
                'data' => $program,
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui program.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified program
     */
    public function destroy(Program $program): JsonResponse
    {
        DB::beginTransaction();
        try {
            // Check if the program has related data before deleting
            $hasClassrooms = $program->classrooms()->exists();
            $hasSubjects = $program->subjects()->exists();

            if ($hasClassrooms || $hasSubjects) {
                return response()->json([
                    'success' => false,
                    'message' => 'Program tidak dapat dihapus karena masih memiliki data terkait.',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $program->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Program berhasil dihapus.',
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus program.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get a list of all programs for API.
     */
    public function getProgramsList(): JsonResponse
    {
        try {
            $programs = Program::select(['id', 'name', 'code', 'status'])
                ->where('status', 'active')
                ->orderBy('name')
                ->get()
                ->map(function ($program) {
                    return [
                        'id' => $program->id,
                        'name' => $program->name,
                        'code' => $program->code,
                        'status' => $program->status?->label
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'Daftar program berhasil dimuat.',
                'data' => $programs,
            ]);

        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat daftar program.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
